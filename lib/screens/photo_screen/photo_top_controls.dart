import 'package:flutter/material.dart';
import 'package:mural/widgets/my_button.dart';

class PhotoTopControls extends StatelessWidget {
  const PhotoTopControls({
    super.key,
    required this.onBack,
    required this.address,
    required this.onAddressPressed,
  });

  final VoidCallback onBack;
  final String address;
  final VoidCallback onAddressPressed;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 40,
      left: 16,
      child: Row(
        spacing: 16,
        children: <Widget>[
          MyButton.icon(
            icon: Icons.arrow_back,
            onPressed: onBack,
          ),
          MyButton(
            borderRadius: 8,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            onPressed: onAddressPressed,
            child: Text(address),
          ),
        ],
      ),
    );
  }
}
