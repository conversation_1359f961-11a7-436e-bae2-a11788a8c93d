import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mural/models/mural.dart';

class PhotoViewer extends StatelessWidget {
  const PhotoViewer({
    super.key,
    required this.imageUrl,
    required this.isLocal,
    required this.showOriginal,
    required this.transformationController,
    required this.onTap,
    required this.onDoubleTapDown,
    required this.onDoubleTap,
    required this.onInteractionUpdate,
    required this.getImagePath,
    required this.cleanupCorruptedImage,
    required this.buildPhotoWidget,
    this.photoMetadata,
  });

  final String imageUrl;
  final bool isLocal;
  final bool showOriginal;
  final TransformationController transformationController;
  final VoidCallback onTap;
  final Function(TapDownDetails) onDoubleTapDown;
  final VoidCallback onDoubleTap;
  final Function(ScaleUpdateDetails) onInteractionUpdate;
  final Future<String> Function(bool) getImagePath;
  final Function(String) cleanupCorruptedImage;
  final Widget Function(bool) buildPhotoWidget;
  final MuralMetadata? photoMetadata;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onDoubleTapDown: onDoubleTapDown,
      onDoubleTap: onDoubleTap,
      child: InteractiveViewer(
        transformationController: transformationController,
        panEnabled: true,
        minScale: 0.5,
        maxScale: 4,
        onInteractionUpdate: onInteractionUpdate,
        child: isLocal
            ? FutureBuilder<String>(
                future: getImagePath(showOriginal),
                builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
                  if (snapshot.hasData) {
                    return Image.file(
                      File(snapshot.data!),
                      errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
                        cleanupCorruptedImage(snapshot.data!);
                        return buildPhotoWidget(true);
                      },
                    );
                  }
                  return buildPhotoWidget(true);
                },
              )
            : Image.network(imageUrl),
      ),
    );
  }
}
