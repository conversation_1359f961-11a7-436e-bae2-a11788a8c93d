import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/services/photo_screen_service.dart';
import 'package:mural/widgets/adaptive_landsacpe.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/photo_widget.dart';
import 'package:mural/widgets/rectangle_adjuster.dart';
import 'package:mural/widgets/working.dart';

// Top-level function for running in a separate isolate
Future<List<Offset>> _calculateImageCornersInBackground(Map<String, dynamic> args) async {
  final PhotoScreenService photoScreenService = PhotoScreenService();
  return await photoScreenService.calculateImageCorners(
    args['path'] as String,
    args['maxWidth'] as double,
    args['maxHeight'] as double,
  );
}

class PhotoEditingView extends StatefulWidget {
  const PhotoEditingView({
    super.key,
    required this.editedImageFile,
    required this.imageHash,
    required this.photoMetadata,
    required this.isLocal,
    required this.adjustedCorners,
    required this.onCornersChanged,
    required this.calculateImageCorners,
    required this.getImageBytes,
    required this.hasRectangleChanged,
    required this.adjustmentApplied,
    required this.isSubmitting,
    required this.isLandscape,
    required this.rotationAngle,
    required this.imageRotation,
    required this.onCancel,
    required this.onApply,
    required this.onSubmit,
    required this.onReset,
    required this.onRotate,
    required this.canReset,
  });

  final XFile? editedImageFile;
  final String imageHash;
  final MuralMetadata? photoMetadata;
  final bool isLocal;
  final List<Offset> adjustedCorners;
  final Function(List<Offset>) onCornersChanged;
  final Future<List<Offset>> Function(String, double, double) calculateImageCorners;
  final Future<Uint8List> Function(String) getImageBytes;
  final bool hasRectangleChanged;
  final bool adjustmentApplied;
  final bool isSubmitting;
  final bool isLandscape;
  final double rotationAngle;
  final double imageRotation;
  final VoidCallback onCancel;
  final VoidCallback onApply;
  final VoidCallback onSubmit;
  final VoidCallback onReset;
  final VoidCallback onRotate;
  final bool canReset;

  @override
  State<PhotoEditingView> createState() => _PhotoEditingViewState();
}

class _PhotoEditingViewState extends State<PhotoEditingView> {
  bool _isLoading = false;
  bool _isDetecting = false;

  Future<void> _runAutoDetection(BoxConstraints constraints, List<Offset> fallbackCorners) async {
    if (widget.editedImageFile == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isDetecting = true;
    });

    try {
      // Use calculateImageCorners which already includes auto-detection
      final List<Offset> detectedCorners = await compute(_calculateImageCornersInBackground, <String, dynamic>{
        'path': widget.editedImageFile!.path,
        'maxWidth': constraints.maxWidth - 40,
        'maxHeight': constraints.maxHeight - 40,
      });

      // Apply the detected corners with padding
      final List<Offset> paddedDetectedCorners = detectedCorners
          .map((Offset corner) => Offset(corner.dx + 20, corner.dy + 20))
          .toList();

      widget.onCornersChanged(paddedDetectedCorners);
    } catch (e) {
      // Silently fail and keep existing corners
    } finally {
      setState(() {
        _isLoading = false;
        _isDetecting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.editedImageFile == null) {
      return working();
    }

    if (widget.isSubmitting) {
      return Center(
        child: MyButton(
          onPressed: () {},
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16,
              children: <Widget>[
                working(),
                const Text('Submitting...', style: TextStyle(color: Colors.white)),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: <Widget>[
        Expanded(
          child: _buildImageDisplayArea(context),
        ),
        // Hide bottom action items while detection is running.
        if (!_isDetecting)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildButtons(),
          ),
      ],
    );
  }

  Widget _buildImageDisplayArea(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return Stack(
          fit: StackFit.expand,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Center(
                child: Transform.rotate(
                  angle: widget.imageRotation,
                  child: PhotoWidget(
                    photo: Photo(
                      imageHash: widget.imageHash,
                      imagePath: widget.isLocal ? widget.editedImageFile!.path : null,
                      imageUrl: !widget.isLocal ? widget.editedImageFile!.path : null,
                      metadata: widget.photoMetadata ?? const MuralMetadata(),
                      isLocal: widget.isLocal,
                    ),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            if (widget.adjustedCorners.isNotEmpty)
              RectangleAdjuster(
                initialCorners: widget.adjustedCorners,
                onCornersChanged: widget.onCornersChanged,
                onAutoDetect: () => _runAutoDetection(constraints, widget.adjustedCorners),
              ),
            if (_isLoading)
              Container(
                color: Colors.black.withAlpha(127),
                child: Center(
                  child: working(),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildButtons() {
    return adaptiveLandscape(
      isLandscape: widget.isLandscape,
      angle: widget.rotationAngle,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          MyButton(
            borderRadius: 20,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            onPressed: widget.onCancel,
            child: const Text('Cancel', style: TextStyle(color: Colors.white)),
          ),
          MyButton.icon(
            borderRadius: 20,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            onPressed: widget.onRotate,
            icon: Icons.rotate_right,
          ),
          if (widget.adjustmentApplied && widget.canReset)
            MyButton(
              borderRadius: 20,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              onPressed: widget.onReset,
              child: const Text('Reset', style: TextStyle(color: Colors.white)),
            ),
          if (widget.hasRectangleChanged)
            MyButton(
              isPrimary: true,
              borderRadius: 20,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              onPressed: widget.onApply,
              child: const Text('Apply', style: TextStyle(color: Colors.white)),
            )
          else if (widget.adjustmentApplied)
            MyButton(
              isPrimary: true,
              borderRadius: 20,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              onPressed: widget.isSubmitting ? null : widget.onSubmit,
              child: widget.isSubmitting
                  ? SizedBox(width: 16, height: 16, child: working())
                  : const Text('Submit', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }
}
