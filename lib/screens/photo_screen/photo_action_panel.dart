import 'package:flutter/material.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/working.dart';

class PhotoActionPanel extends StatelessWidget {
  const PhotoActionPanel({
    super.key,
    required this.hasModifiedVersion,
    required this.showOriginal,
    required this.onToggleOriginal,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.onOpenMap,
    required this.isImageAvailable,
    required this.canEdit,
    required this.canDelete,
    required this.onEdit,
    required this.onDelete,
    required this.isLocal,
    required this.onSubmit,
    required this.isSubmitting,
  });

  final bool hasModifiedVersion;
  final bool showOriginal;
  final VoidCallback onToggleOriginal;
  final double? currentLatitude;
  final double? currentLongitude;
  final VoidCallback onOpenMap;
  final bool isImageAvailable;
  final bool canEdit;
  final bool canDelete;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final bool isLocal;
  final VoidCallback onSubmit;
  final bool isSubmitting;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 40,
      left: 0,
      right: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              if (currentLatitude != null && currentLongitude != null)
                _buildActionButton(Icons.location_on_outlined, 'Direction', onOpenMap),
              if (isImageAvailable && canEdit) _buildActionButton(Icons.crop, 'Edit', onEdit),
              if (canDelete) _buildActionButton(Icons.delete_outline_outlined, 'Delete', onDelete),
              if (isLocal)
                _buildActionButton(
                  Icons.cloud_upload_outlined,
                  'Submit',
                  onSubmit,
                  isLoading: isSubmitting,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed, {bool isLoading = false}) {
    return MyButton(
      onPressed: onPressed,
      isEnabled: !isLoading,
      borderRadius: 20,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: SizedBox(
        width: 50,
        child: isLoading
            ? SizedBox(width: 24, height: 24, child: working())
            : Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 2,
                children: <Widget>[
                  Icon(icon, color: Colors.white),
                  Text(label, style: const TextStyle(color: Colors.white, fontSize: 8)),
                ],
              ),
      ),
    );
  }
}
