import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:camera/camera.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:mural/constants/status_constants.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/screens/address_screen.dart';
import 'package:mural/screens/photo_screen/photo_action_panel.dart';
import 'package:mural/screens/photo_screen/photo_editing_view.dart';
import 'package:mural/screens/photo_screen/photo_top_controls.dart';
import 'package:mural/screens/photo_screen/photo_viewer.dart';
import 'package:mural/services/auth_service.dart';
import 'package:mural/services/backend_sync_service.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/services/photo_screen_service.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_message.dart';
import 'package:mural/widgets/photo_widget.dart';
import 'package:mural/widgets/working.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:url_launcher/url_launcher.dart';

// Generic interface for photo documents
abstract class PhotoDocument {
  Map<String, dynamic> data();
  String get id;
}

class PhotoScreen extends StatefulWidget {
  const PhotoScreen({
    super.key,
    required this.imageUrl,
    this.address,
    this.latitude,
    this.longitude,
    this.docId,
    this.isLocal = false,
    this.onPhotoSubmitted,
    this.initialEditingMode = false,
    this.onPhotoDeleted,
    this.photoList,
    this.currentIndex,
    this.isReviewMode = false,
  });

  final String imageUrl;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? docId;
  final bool isLocal;
  final bool initialEditingMode;
  final VoidCallback? onPhotoDeleted;
  final VoidCallback? onPhotoSubmitted;
  final List<PhotoDocument>? photoList;
  final int? currentIndex;
  final bool isReviewMode;

  @override
  State<PhotoScreen> createState() => _PhotoScreenState();
}

class _PhotoScreenState extends State<PhotoScreen> {
  final MuralMetadataService _muralMetadataService = MuralMetadataService();
  final PhotoScreenService _photoScreenService = PhotoScreenService();
  // Editing state
  bool _isEditingMode = false;
  bool _hasRectangleChanged = false;
  bool _isSubmitting = false;
  bool _isImageAvailable = true; // Track if image file is available for editing
  XFile? _editedImageFile;
  List<Offset> _adjustedCorners = <Offset>[];

  bool _adjustmentApplied = false;
  bool _isLandscape = false;
  double _imageRotation = 0.0; // 90-degree rotation state

  // Metadata state
  MuralMetadata? _photoMetadata;

  // Orientation state
  StreamSubscription<dynamic>? _subscription;
  double _rotationAngle = 0;
  bool _showOverlays = true;
  bool _showOriginal = false; // Toggle for Original/Modified view

  // Swipe navigation state
  int _currentIndex = 0;
  String _currentImageUrl = '';
  String? _currentAddress;
  double? _currentLatitude;
  double? _currentLongitude;
  String? _currentDocId;

  // PageView controller
  late PageController _pageController;

  // Zoom state for double-tap functionality
  final TransformationController _transformationController = TransformationController();
  bool _isZoomed = false;

  /// Check if the image file is available for editing
  Future<void> _checkImageAvailability() async {
    if (widget.isLocal) {
      final String imagePath = await _getImagePath();
      final File file = File(imagePath);
      final bool exists = await file.exists();
      if (mounted) {
        setState(() {
          _isImageAvailable = exists;
        });
      }
    }
    // For remote images, assume they're available (will be checked when editing starts)
  }

  /// Get the full image path for local photos
  Future<String> _getImagePath([bool showOriginal = false]) async {
    if (_currentImageUrl.contains('/')) {
      // Full path provided
      if (showOriginal && _hasModifiedVersion()) {
        // Return original version if it exists
        final String originalPath = _currentImageUrl.replaceAll('.jpg', '_original.jpg');
        final File originalFile = File(originalPath);
        if (await originalFile.exists()) {
          return originalPath;
        }
      }
      return _currentImageUrl;
    } else {
      // Hash provided, construct full path
      final String imageCacheDirPath = await _muralMetadataService.getImageCacheDirPath();
      if (showOriginal && _hasModifiedVersion()) {
        // Return original version if it exists
        final String originalPath = '$imageCacheDirPath/${_currentImageUrl}_original.jpg';
        final File originalFile = File(originalPath);
        if (await originalFile.exists()) {
          return originalPath;
        }
      }
      return '$imageCacheDirPath/$_currentImageUrl.jpg';
    }
  }

  String get _imageHash {
    if (_currentImageUrl.contains('/')) {
      // Full path provided
      return p.basenameWithoutExtension(_currentImageUrl);
    } else {
      // Just hash provided
      return _currentImageUrl;
    }
  }

  /// Load metadata for local photos
  Future<void> _loadMetadata() async {
    if (widget.isLocal) {
      final MuralMetadata? metadata = await _muralMetadataService.getPhotoMetadata(_imageHash);
      if (mounted) {
        setState(() {
          _photoMetadata = metadata;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex ?? 0;
    _pageController = PageController(initialPage: _currentIndex);
    _updateCurrentPhoto();
    _checkImageAvailability();
    _loadMetadata();
    if (widget.initialEditingMode) {
      _enterEditingMode();
    }
    if (!kIsWeb && defaultTargetPlatform != TargetPlatform.macOS) {
      _subscription = accelerometerEventStream().listen((AccelerometerEvent event) {
        final double x = event.x;
        final double y = event.y;
        final double rotationAngle = math.atan2(x, y);
        _isLandscape = x.abs() > y.abs();
        setState(() {
          _rotationAngle = rotationAngle;
        });
      });
    }
  }

  void _updateCurrentPhoto() {
    if (widget.photoList != null && _currentIndex < widget.photoList!.length) {
      final PhotoDocument doc = widget.photoList![_currentIndex];
      final Map<String, dynamic> data = doc.data();
      _currentImageUrl = data['imageUrl'];
      _currentAddress = data['address'];
      _currentLatitude = data['latitude'];
      _currentLongitude = data['longitude'];
      _currentDocId = doc.id;
    } else {
      _currentImageUrl = widget.imageUrl;
      _currentAddress = widget.address;
      _currentLatitude = widget.latitude;
      _currentLongitude = widget.longitude;
      _currentDocId = widget.docId;
    }
  }

  void _navigateToPhoto(int newIndex) {
    if (widget.photoList == null || newIndex < 0 || newIndex >= widget.photoList!.length) {
      return;
    }
    _pageController.animateToPage(
      newIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
      _updateCurrentPhoto();
      _photoMetadata = null;
    });
    _loadMetadata();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _pageController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  Future<void> _enterEditingMode() async {
    // Check if user can edit this photo
    if (!_canEditPhoto()) {
      MyMessage.show('Cannot edit: Photo has been approved');
      return;
    }

    try {
      File? file;

      if (widget.isLocal) {
        // For local photos, use the file directly
        final String imagePath = await _getImagePath();
        file = File(imagePath);
        if (!await file.exists()) {
          setState(() {
            _isImageAvailable = false;
          });
          MyMessage.show('Local image file not found: $imagePath');
          return;
        }
        // Load comprehensive metadata from XMP for local files
        final String imageHash = p.basenameWithoutExtension(file.path);
        final MuralMetadata? metadata = await _muralMetadataService.getPhotoMetadata(imageHash);
        if (metadata != null) {
          // Load existing crop corners if available
          if (metadata.cropCorners != null && metadata.cropCorners!.isNotEmpty) {
            _adjustedCorners = metadata.cropCorners!;

            _hasRectangleChanged = false;
          }

          // Check if distortion correction was previously applied by checking if corners exist
          if (metadata.cropCorners != null && metadata.cropCorners!.isNotEmpty) {
            _adjustmentApplied = true;
          }
        }
      } else {
        // For remote photos, handle differently on web vs mobile
        if (kIsWeb) {
          // On web, we'll work directly with the URL in editing mode
          file = File(widget.imageUrl); // Use URL as file path for web
        } else {
          // For mobile, download to temp directory
          final http.Response response = await http.get(Uri.parse(widget.imageUrl));
          if (response.statusCode != 200) {
            MyMessage.show('Failed to download image: HTTP ${response.statusCode}');
            return;
          }
          final Directory tempDir = await getTemporaryDirectory();
          final String tempPath = tempDir.path;
          final String fileName = p.basename(widget.imageUrl).split('?').first;
          file = File('$tempPath/$fileName');
          await file.writeAsBytes(response.bodyBytes);
        }
      }

      setState(() {
        _isEditingMode = true;
        _editedImageFile = XFile(file!.path);
        _hasRectangleChanged = false;
        _adjustmentApplied = widget.isLocal ? _hasModifiedVersion() : true;
      });

      // Calculate image-based corners after state is set
      _calculateDefaultCorners().then((List<Offset> defaultCorners) {
        setState(() {
          _adjustedCorners = defaultCorners;
        });
      });
    } catch (e) {
      MyMessage.show('Error loading image for editing: $e');
    }
  }

  void _exitEditingMode() {
    if (widget.initialEditingMode) {
      // If editing started from CameraScreen photo taken, pop back to camera view
      Navigator.of(context).pop();
      return;
    }
    setState(() {
      _isEditingMode = false;
      _editedImageFile = null;
      _hasRectangleChanged = false;
      _adjustmentApplied = false;
      _imageRotation = 0.0;
    });
  }

  void _rotate90() {
    setState(() {
      _imageRotation = (_imageRotation + 1.5708) % 6.2832; // 90 degrees in radians
    });
  }

  void _openMap(BuildContext context, double lat, double lng) async {
    final String url = 'https://www.google.com/maps/search/?api=1&query=$lat,$lng';
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      // ignore: use_build_context_synchronously
      MyMessage.show('Could not open the map.');
    }
  }

  void _showDeleteConfirmation(BuildContext context) {
    if (_currentDocId == null) {
      MyMessage.show('Cannot delete: Photo ID not available');
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Delete Photo'),
        content: const Text('Are you sure you want to delete this photo? This action cannot be undone.'),
        actions: <Widget>[
          OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(foregroundColor: Colors.black),
            child: const Text('Cancel'),
          ),
          OutlinedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
              _deletePhoto(_currentDocId!);

              // Refresh the list
              if (widget.onPhotoDeleted != null) {
                widget.onPhotoDeleted!();
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _openAddressScreen(BuildContext context) async {
    final String? newAddress = await Navigator.push(
      context,
      MaterialPageRoute<String>(
        builder: (BuildContext context) => AddressScreen(
          initialAddress: _photoMetadata?.address ?? _currentAddress,
          latitude: _currentLatitude,
          longitude: _currentLongitude,
        ),
      ),
    );

    if (newAddress != null) {
      _updateAddress(newAddress);
    }
  }

  Future<void> _updateAddress(String newAddress) async {
    final MuralMetadata updatedMetadata =
        (_photoMetadata ??
                MuralMetadata(
                  lastModified: DateTime.now(),
                  latitude: widget.latitude,
                  longitude: widget.longitude,
                ))
            .copyWith(address: newAddress);

    setState(() {
      _photoMetadata = updatedMetadata;
    });

    try {
      if (kIsWeb) {
        // On web, create and upload updated XMP to Firebase Storage
        if (widget.docId != null && widget.docId!.isNotEmpty) {
          final User? user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            // Get the original document to find the original user
            final DocumentSnapshot<Map<String, dynamic>> doc = await FirebaseFirestore.instance
                .collection('murals')
                .doc(widget.docId!)
                .get();

            final String originalUserId = doc.data()?['userId'] ?? '';
            final String xmpContent = await _photoScreenService.createMuralMetadataContent(updatedMetadata);

            // Upload XMP to the original user's folder to maintain structure
            final Reference xmpRef = FirebaseStorage.instance.ref().child('murals/$originalUserId/$_imageHash.xmp');
            await xmpRef.putString(xmpContent);
            final String xmpUrl = await xmpRef.getDownloadURL();

            await FirebaseFirestore.instance.collection('murals').doc(widget.docId!).update(<String, dynamic>{
              'address': newAddress,
              'xmpUrl': xmpUrl,
              'lastUpdated': FieldValue.serverTimestamp(),
              'lastModifiedBy': user.uid,
            });
          }
          MyMessage.show('Address updated successfully.');
        } else {
          MyMessage.show('Cannot update address: Photo ID not available.');
        }
      } else {
        // On mobile, update XMP and Firebase if needed
        if (widget.isLocal) {
          await _muralMetadataService.updateMetadata(
            imageHash: _imageHash,
            address: newAddress,
            latitude: updatedMetadata.latitude,
            longitude: updatedMetadata.longitude,
          );
          MyMessage.show('Address updated locally.');
        } else {
          await _muralMetadataService.updateMetadata(
            imageHash: _imageHash,
            address: newAddress,
            latitude: updatedMetadata.latitude,
            longitude: updatedMetadata.longitude,
          );

          if (widget.docId != null && widget.docId!.isNotEmpty) {
            await FirebaseFirestore.instance.collection('murals').doc(widget.docId!).update(<String, dynamic>{
              'address': newAddress,
              'lastUpdated': FieldValue.serverTimestamp(),
            });
            MyMessage.show('Address updated successfully.');
          } else {
            MyMessage.show('Address updated locally.');
          }
        }
      }
    } catch (e) {
      debugPrint('Error saving address: $e');
      MyMessage.show('Failed to save address: $e');
    }
  }

  void _deletePhoto(String docId) async {
    await _photoScreenService.deletePhoto(widget.imageUrl, docId, widget.isLocal);
  }

  @override
  Widget build(BuildContext context) {
    return GradientScreen(
      child: KeyboardListener(
        focusNode: FocusNode()..requestFocus(),
        onKeyEvent: (KeyEvent event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              Navigator.of(context).pop();
            } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
              if (_currentIndex > 0) {
                _navigateToPhoto(_currentIndex - 1);
              }
            } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
              if (widget.photoList != null && _currentIndex < widget.photoList!.length - 1) {
                _navigateToPhoto(_currentIndex + 1);
              }
            }
          }
        },
        child: _isEditingMode ? _buildEditingView() : _buildViewingView(),
      ),
    );
  }

  Widget _buildViewingView() {
    return Stack(
      children: <Widget>[
        _buildFullScreenImage(),
        if (_showOverlays)
          PhotoActionPanel(
            hasModifiedVersion: _hasModifiedVersion(),
            showOriginal: _showOriginal,
            onToggleOriginal: () => setState(() => _showOriginal = !_showOriginal),
            currentLatitude: _currentLatitude,
            currentLongitude: _currentLongitude,
            onOpenMap: () => _openMap(context, _currentLatitude!, _currentLongitude!),
            isImageAvailable: _isImageAvailable,
            canEdit: _canEditPhoto(),
            canDelete: _canDeletePhoto(),
            onEdit: _enterEditingMode,
            onDelete: () => _showDeleteConfirmation(context),
            isLocal: widget.isLocal,
            onSubmit: _submitPhoto,
            isSubmitting: _isSubmitting,
          ),
        if (_showOverlays)
          PhotoTopControls(
            onBack: () => Navigator.of(context).pop(),
            address: _photoMetadata?.address ?? _currentAddress ?? 'Unknown location',
            onAddressPressed: () => _openAddressScreen(context),
          ),
        if (_isSubmitting) _buildSubmittingOverlay(),
      ],
    );
  }

  Widget _buildFullScreenImage() {
    if (widget.photoList != null) {
      return Positioned.fill(
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          itemCount: widget.photoList!.length,
          itemBuilder: (BuildContext context, int index) => _buildPhotoPage(index),
        ),
      );
    } else {
      return Positioned.fill(
        child: _buildPhotoPage(0),
      );
    }
  }

  Widget _buildPhotoPage(int index) {
    Offset? doubleTapPosition;

    return PhotoViewer(
      imageUrl: _currentImageUrl,
      isLocal: widget.isLocal,
      showOriginal: _showOriginal,
      transformationController: _transformationController,
      onTap: () => setState(() => _showOverlays = !_showOverlays),
      onDoubleTapDown: (TapDownDetails details) => doubleTapPosition = details.localPosition,
      onDoubleTap: () {
        if (_isZoomed) {
          _transformationController.value = Matrix4.identity();
          setState(() => _isZoomed = false);
        } else if (doubleTapPosition != null) {
          final double scale = 3.0;
          final double dx = doubleTapPosition!.dx * (1 - scale);
          final double dy = doubleTapPosition!.dy * (1 - scale);
          _transformationController.value = Matrix4.identity()
            ..translate(dx, dy)
            ..scale(scale);
          setState(() => _isZoomed = true);
        }
      },
      onInteractionUpdate: (ScaleUpdateDetails details) =>
          setState(() => _isZoomed = _transformationController.value.getMaxScaleOnAxis() > 1.0),
      getImagePath: _getImagePath,
      cleanupCorruptedImage: _photoScreenService.cleanupCorruptedImage,
      buildPhotoWidget: _buildPhotoWidget,
      photoMetadata: _photoMetadata,
    );
  }

  Widget _buildSubmittingOverlay() {
    return Positioned.fill(
      child: ColoredBox(
        color: Colors.black.withAlpha(150),
        child: SizedBox.square(
          dimension: 20,
          child: working(),
        ),
      ),
    );
  }

  Future<List<Offset>> _calculateDefaultCorners() async {
    if (_editedImageFile == null) {
      return <Offset>[];
    }

    try {
      final double screenWidth = MediaQuery.of(context).size.width;
      final double screenHeight = MediaQuery.of(context).size.height;

      final List<Offset> defaultCorners = await _photoScreenService.calculateImageCorners(
        _editedImageFile!.path,
        screenWidth - 40, // account for padding
        screenHeight - 40, // account for padding
      );

      // Apply padding to the corners
      return defaultCorners.map((Offset corner) => Offset(corner.dx + 20, corner.dy + 20)).toList();
    } catch (e) {
      // Return empty list if calculation fails
      return <Offset>[];
    }
  }

  void _resetCropping() async {
    final List<Offset> defaultCorners = await _calculateDefaultCorners();
    setState(() {
      _adjustedCorners = defaultCorners;
      _hasRectangleChanged = false;
      _adjustmentApplied = false;
    });
  }

  Widget _buildEditingView() {
    // If adjustedCorners is empty, calculate image-based corners
    if (_adjustedCorners.isEmpty && _editedImageFile != null) {
      return FutureBuilder<List<Offset>>(
        future: _calculateDefaultCorners(),
        builder: (BuildContext context, AsyncSnapshot<List<Offset>> snapshot) {
          if (snapshot.hasData && snapshot.data!.isNotEmpty) {
            // Update _adjustedCorners with the calculated image-based corners
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_adjustedCorners.isEmpty) {
                setState(() {
                  _adjustedCorners = snapshot.data!;
                });
              }
            });

            return _buildPhotoEditingView(snapshot.data!);
          }

          // Show loading while calculating corners
          return const Center(child: CircularProgressIndicator());
        },
      );
    }

    return _buildPhotoEditingView(_adjustedCorners);
  }

  Widget _buildPhotoEditingView(List<Offset> corners) {
    return PhotoEditingView(
      editedImageFile: _editedImageFile,
      imageHash: _imageHash,
      photoMetadata: _photoMetadata,
      isLocal: widget.isLocal,
      adjustedCorners: corners,
      onCornersChanged: (List<Offset> corners) => setState(() {
        _adjustedCorners = corners;
        _hasRectangleChanged = true;
      }),
      calculateImageCorners: _photoScreenService.calculateImageCorners,
      getImageBytes: _photoScreenService.getImageBytes,
      hasRectangleChanged: _hasRectangleChanged,
      adjustmentApplied: _adjustmentApplied,
      isSubmitting: _isSubmitting,
      isLandscape: _isLandscape,
      rotationAngle: _rotationAngle,
      imageRotation: _imageRotation,
      onCancel: _exitEditingMode,
      onReset: _resetCropping,
      onApply: _applyRectangleAdjustment,
      onSubmit: widget.isLocal ? _submitPhoto : _saveChanges,
      onRotate: _rotate90,
      canReset: _canResetCropping(),
    );
  }

  Future<void> _applyRectangleAdjustment() async {
    if (_editedImageFile == null) {
      return;
    }

    // Show processing state immediately
    setState(() {
      _isSubmitting = true;
    });

    try {
      final Uint8List bytes = await _photoScreenService.getImageBytes(_editedImageFile!.path);
      final img.Image? originalImage = img.decodeImage(bytes);
      if (originalImage == null) {
        MyMessage.show('Error: Could not decode image.');
        return;
      }

      if (!mounted) {
        return;
      }

      final double screenWidth = MediaQuery.of(context).size.width;
      final double screenHeight = MediaQuery.of(context).size.height;
      final List<Offset> displayCorners = await _photoScreenService.calculateImageCorners(
        _editedImageFile!.path,
        screenWidth - 40, // account for padding
        screenHeight - 40, // account for padding
      );

      final double scaleX = originalImage.width / (displayCorners[1].dx - displayCorners[0].dx);
      final double scaleY = originalImage.height / (displayCorners[2].dy - displayCorners[0].dy);

      // Convert screen coordinates to image coordinates
      final List<Offset> imageCorners = _adjustedCorners.map((Offset corner) {
        return Offset(
          (corner.dx - (displayCorners[0].dx + 20)) * scaleX,
          (corner.dy - (displayCorners[0].dy + 20)) * scaleY,
        );
      }).toList();

      // Find the bounding box of the quadrilateral
      final double minX = imageCorners.map((Offset p) => p.dx).reduce((double a, double b) => a < b ? a : b);
      final double maxX = imageCorners.map((Offset p) => p.dx).reduce((double a, double b) => a > b ? a : b);
      final double minY = imageCorners.map((Offset p) => p.dy).reduce((double a, double b) => a < b ? a : b);
      final double maxY = imageCorners.map((Offset p) => p.dy).reduce((double a, double b) => a > b ? a : b);

      final int newWidth = (maxX - minX).round();
      final int newHeight = (maxY - minY).round();

      if (kIsWeb) {
        // On web, use a more efficient approach to avoid hanging
        await _processImageOnWeb(originalImage, imageCorners, newWidth, newHeight);
      } else {
        await _processImageOnMobile(originalImage, imageCorners, newWidth, newHeight);
      }
    } catch (e) {
      MyMessage.show('Error correcting perspective: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<void> _processImageOnWeb(
    img.Image originalImage,
    List<Offset> imageCorners,
    int newWidth,
    int newHeight,
  ) async {
    // Create a smaller preview for web to avoid hanging
    const int maxDimension = 1024;
    final double scale = math.min(1.0, maxDimension / math.max(newWidth, newHeight));
    final int scaledWidth = (newWidth * scale).round();
    final int scaledHeight = (newHeight * scale).round();

    final img.Image newImage = img.Image(width: scaledWidth, height: scaledHeight);

    // Process in smaller chunks to avoid blocking
    const int chunkSize = 100;
    for (int startY = 0; startY < scaledHeight; startY += chunkSize) {
      final int endY = math.min(startY + chunkSize, scaledHeight);

      for (int y = startY; y < endY; y++) {
        for (int x = 0; x < scaledWidth; x++) {
          final double u = x / scaledWidth;
          final double v = y / scaledHeight;

          final double originalX =
              (imageCorners[0].dx * (1 - u) * (1 - v)) +
              (imageCorners[1].dx * u * (1 - v)) +
              (imageCorners[2].dx * u * v) +
              (imageCorners[3].dx * (1 - u) * v);

          final double originalY =
              (imageCorners[0].dy * (1 - u) * (1 - v)) +
              (imageCorners[1].dy * u * (1 - v)) +
              (imageCorners[2].dy * u * v) +
              (imageCorners[3].dy * (1 - u) * v);

          final img.Pixel pixel = originalImage.getPixel(originalX.round(), originalY.round());
          newImage.setPixel(x, y, pixel);
        }
      }

      // Yield control back to the event loop
      await Future<void>.delayed(Duration.zero);

      if (!mounted) {
        return;
      }
    }

    final Uint8List warpedBytes = img.encodeJpg(newImage, quality: 85);
    final String base64Image = base64Encode(warpedBytes);
    final String dataUrl = 'data:image/jpeg;base64,$base64Image';

    if (mounted) {
      setState(() {
        _editedImageFile = XFile(dataUrl);
        _hasRectangleChanged = false;
        _adjustmentApplied = true;
      });
    }
  }

  Future<void> _processImageOnMobile(
    img.Image originalImage,
    List<Offset> imageCorners,
    int newWidth,
    int newHeight,
  ) async {
    final img.Image newImage = img.Image(width: newWidth, height: newHeight);

    for (int y = 0; y < newHeight; y++) {
      for (int x = 0; x < newWidth; x++) {
        final double u = x / newWidth;
        final double v = y / newHeight;

        final double originalX =
            (imageCorners[0].dx * (1 - u) * (1 - v)) +
            (imageCorners[1].dx * u * (1 - v)) +
            (imageCorners[2].dx * u * v) +
            (imageCorners[3].dx * (1 - u) * v);

        final double originalY =
            (imageCorners[0].dy * (1 - u) * (1 - v)) +
            (imageCorners[1].dy * u * (1 - v)) +
            (imageCorners[2].dy * u * v) +
            (imageCorners[3].dy * (1 - u) * v);

        final img.Pixel pixel = originalImage.getPixel(originalX.round(), originalY.round());
        newImage.setPixel(x, y, pixel);
      }
    }

    final File warpedFile = File('${_editedImageFile!.path}_warped.jpg');
    await warpedFile.writeAsBytes(img.encodeJpg(newImage, quality: 90));

    if (!mounted) {
      return;
    }

    final double newScreenWidth = MediaQuery.of(context).size.width;
    final double newScreenHeight = MediaQuery.of(context).size.height;
    final List<Offset> newDisplayCorners = await _photoScreenService.calculateImageCorners(
      warpedFile.path,
      newScreenWidth - 40, // account for padding
      newScreenHeight - 40, // account for padding
    );

    final List<Offset> newPaddedCorners = newDisplayCorners
        .map((Offset p) => Offset(p.dx + 20, p.dy + 20)) // account for padding
        .toList();

    setState(() {
      _editedImageFile = XFile(warpedFile.path);
      _adjustedCorners = newPaddedCorners;
      _hasRectangleChanged = false;
      _adjustmentApplied = true;
    });
  }

  Future<void> _saveChanges() async {
    if (_editedImageFile == null) {
      MyMessage.show('No changes to save.');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final Uint8List imageBytes = await _photoScreenService.getImageBytes(_editedImageFile!.path);

      if (widget.isLocal) {
        // For local photos, save directly to the original file
        if (kIsWeb) {
          MyMessage.show('Cannot save local changes on web - use Submit instead');
          return;
        }
        final String imagePath = await _getImagePath();
        final File originalFile = File(imagePath);
        await originalFile.writeAsBytes(imageBytes);

        // Update existing metadata with crop corners
        await _muralMetadataService.updateCropCorners(
          _imageHash,
          _adjustedCorners.isNotEmpty ? _adjustedCorners : <Offset>[],
        );
        MyMessage.show('Local photo updated successfully!');
      } else {
        // For remote photos, upload to Firebase Storage
        if (widget.docId == null || widget.docId!.isEmpty) {
          MyMessage.show('Photo ID is missing.');
          return;
        }

        final Reference storageRef = FirebaseStorage.instance.refFromURL(widget.imageUrl);

        // Upload the new image, overwriting the old one
        await storageRef.putData(imageBytes);

        // Update allowed fields according to Firestore rules
        await FirebaseFirestore.instance.collection('murals').doc(widget.docId!).update(<String, Object>{
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        MyMessage.show('Changes saved successfully!');
      }

      _exitEditingMode();
    } catch (e) {
      MyMessage.show('Error saving changes: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<void> _submitPhoto() async {
    if (widget.imageUrl.isEmpty || widget.latitude == null || widget.longitude == null) {
      MyMessage.show('Missing image or location data');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        MyMessage.show('Please sign in first to submit photos');
        final AuthService authService = AuthService();
        final UserCredential? result = await authService.signInWithGoogle();
        if (result == null) {
          setState(() {
            _isSubmitting = false;
          });
          return;
        }
      }

      MuralMetadata? photoMetadata;

      if (kIsWeb) {
        // On web, use the metadata we have in memory or create basic metadata
        photoMetadata =
            _photoMetadata ??
            MuralMetadata(
              latitude: widget.latitude,
              longitude: widget.longitude,
              address: widget.address,
              dateTaken: DateTime.now(),
              cropCorners: _adjustedCorners.isNotEmpty ? _adjustedCorners : null,
            );
      } else {
        photoMetadata = await _muralMetadataService.getPhotoMetadata(_imageHash);
      }

      String? docId;

      if (kIsWeb) {
        final Uint8List imageBytes = await _photoScreenService.getImageBytes(_editedImageFile!.path);
        docId = await _photoScreenService.submitPhotoDirectlyToFirebase(
          user!.uid,
          _imageHash,
          photoMetadata!,
          imageBytes,
        );
      } else {
        final BackendSyncService backendSyncService = BackendSyncService();
        docId = await backendSyncService.submitPhotoWithMetadata(
          imageHash: _imageHash,
          metadata: photoMetadata!,
          checkDuplicates: true,
        );
      }

      if (docId == null) {
        MyMessage.show('Error submitting photo');
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      // Clean up local files after successful upload
      if (!kIsWeb) {
        try {
          final BackendSyncService backendSyncService = BackendSyncService();
          await backendSyncService.cleanupAfterUpload(_imageHash);
        } catch (e) {
          debugPrint('[_submitPhoto] Error cleaning up local files: $e');
        }
      }

      setState(() {
        _isSubmitting = false;
      });
      MyMessage.show('Photo submitted successfully!');
      if (widget.onPhotoSubmitted != null) {
        widget.onPhotoSubmitted!();
      }
      if (context.mounted) {
        // ignore: use_build_context_synchronously
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('Error in _submitPhoto: $e');
      setState(() {
        _isSubmitting = false;
      });
      MyMessage.show('Error submitting photo: $e');
    }
  }

  /// Check if a modified version exists (has crop corners)
  bool _hasModifiedVersion() {
    return _photoMetadata?.cropCorners?.isNotEmpty == true;
  }

  /// Check if current user is a reviewer
  bool _isReviewer() {
    // This would need to be implemented based on your user role system
    // For now, return false as a placeholder
    return false;
  }

  /// Check if current user can edit the photo based on approval status
  bool _canEditPhoto() {
    // If accessed from ReviewScreen, allow editing (only reviewers can access ReviewScreen)
    if (widget.isReviewMode) {
      return true;
    }

    // Reviewer can edit anytime
    if (_isReviewer()) {
      return true;
    }

    // Owner can edit before approval
    if (widget.isLocal) {
      return true; // Local photos are owned by user and not yet submitted
    }

    // For remote photos, check if user is owner and photo is not approved
    final User? currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null && widget.photoList != null && _currentIndex < widget.photoList!.length) {
      final PhotoDocument doc = widget.photoList![_currentIndex];
      final Map<String, dynamic> data = doc.data();
      final String? photoUserId = data['userId'];
      final String? status = data['status'];

      // Owner can edit if photo is not approved (pending or rejected)
      return photoUserId == currentUser.uid && status != StatusConstants.approved;
    }

    return false;
  }

  /// Check if current user can delete the photo based on ownership and approval status
  bool _canDeletePhoto() {
    // If accessed from ReviewScreen, allow deletion (only reviewers can access ReviewScreen)
    if (widget.isReviewMode) {
      return true;
    }

    // Reviewer can delete anytime
    if (_isReviewer()) {
      return true;
    }

    // Owner can delete before approval
    if (widget.isLocal) {
      return true; // Local photos are owned by user and not yet submitted
    }

    // For remote photos, check if user is owner and photo is not approved
    final User? currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null && widget.photoList != null && _currentIndex < widget.photoList!.length) {
      final PhotoDocument doc = widget.photoList![_currentIndex];
      final Map<String, dynamic> data = doc.data();
      final String? photoUserId = data['userId'];
      final String? status = data['status'];

      // Owner can delete if photo is not approved (pending or rejected)
      return photoUserId == currentUser.uid && status != StatusConstants.approved;
    }

    return false;
  }

  /// Check if current user can reset cropping based on ownership and approval status
  bool _canResetCropping() {
    return _canEditPhoto();
  }

  /// Builds a PhotoWidget for the current image
  Widget _buildPhotoWidget(bool isLocal) {
    final MuralMetadata metadata =
        _photoMetadata ??
        MuralMetadata(
          lastModified: DateTime.now(),
        );

    if (isLocal) {
      // For local photos, we need to determine the correct imageHash and imagePath
      String imageHash;
      String? imagePath;

      if (widget.imageUrl.contains('/')) {
        // Full path provided
        imageHash = p.basenameWithoutExtension(widget.imageUrl);
        imagePath = widget.imageUrl;
      } else {
        // Just hash provided
        imageHash = widget.imageUrl;
        imagePath = null; // PhotoWidget will resolve this via PhotoService
      }

      final Photo photo = Photo(
        imageHash: imageHash,
        imagePath: imagePath,
        metadata: metadata,
        isLocal: true,
      );

      return PhotoWidget(photo: photo);
    } else {
      final Photo photo = Photo.fromRemote(
        widget.docId ?? '',
        widget.imageUrl,
        metadata,
      );

      return PhotoWidget(photo: photo);
    }
  }
}
