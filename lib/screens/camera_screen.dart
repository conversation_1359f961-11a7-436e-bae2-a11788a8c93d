// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:camera/camera.dart';
import 'package:exif/exif.dart';
import 'package:file_selector/file_selector.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:mural/constants/my_colors.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/widgets/buttons_selection.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_message.dart';
import 'package:mural/widgets/shutter_button.dart';
import 'package:mural/widgets/spray.dart';
import 'package:mural/widgets/working.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({
    super.key,
    this.rotationAngle = 0.0,
    required this.isLandscape,
    this.isSelected = false,
    this.imageUrl = '',
    this.latitude,
    this.longitude,
    this.address,
    this.onPhotoSubmitted,
  });
  final double rotationAngle;
  final bool isLandscape;
  final bool isSelected;
  final String imageUrl;
  final double? latitude;
  final double? longitude;
  final String? address;
  final VoidCallback? onPhotoSubmitted;

  @override
  CameraScreenState createState() => CameraScreenState();
}

class CameraScreenState extends State<CameraScreen> with WidgetsBindingObserver {
  CameraController? controller;
  CameraDescription? selectedCameraDescription;

  List<CameraDescription> _cameras = <CameraDescription>[];
  XFile? imageFile;

  // For macOS simulation
  List<FileSystemEntity> _fakePhotos = <FileSystemEntity>[];

  // Add these variables to hold the last location
  double? lastLatitude;
  double? lastLongitude;
  String? lastAddress;

  // Camera zoom state
  double _currentZoom = 1.0;
  double _minAvailableZoom = 1.0;
  double _maxAvailableZoom = 1.0;

  // Spray effect state
  bool _showSprayEffect = false;

  final MuralMetadataService _muralMetadataService = MuralMetadataService(); // New instance

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (widget.isSelected) {
      _initializeCamera();
    }
  }

  @override
  void didUpdateWidget(CameraScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _initializeCamera();
      } else {
        _disposeCamera();
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposeCamera();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _disposeCamera();
    }
  }

  Future<void> _initializeCamera() async {
    availableCameras().then((List<CameraDescription> cameras) {
      _cameras = cameras.where((CameraDescription camera) => camera.lensDirection == CameraLensDirection.back).toList();
      selectCamera();
    });
  }

  Future<void> _disposeCamera() async {
    final CameraController? currentController = controller;
    controller = null;
    if (currentController != null && currentController.value.isInitialized) {
      await currentController.dispose();
    }
  }

  Future<void> _loadFakePhotos({String? initialPath}) async {
    if (!Platform.isMacOS) {
      return;
    }

    String? directoryPath = initialPath;
    directoryPath ??= await getDirectoryPath(
      confirmButtonText: 'Select fakeCamera folder',
    );

    if (directoryPath == null) {
      return;
    }

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('fakePhotosPath', directoryPath);

    await _loadFakePhotosFromPath(directoryPath);
  }

  Future<void> _loadFakePhotosFromPath(String directoryPath) async {
    final Directory dir = Directory(directoryPath);
    if (await dir.exists()) {
      final List<FileSystemEntity> imageFiles = dir
          .listSync()
          .where(
            (FileSystemEntity f) =>
                f.path.toLowerCase().endsWith('.jpg') ||
                f.path.toLowerCase().endsWith('.heic') ||
                f.path.toLowerCase().endsWith('.png'),
          )
          .toList();
      if (mounted) {
        setState(() {
          _fakePhotos = imageFiles;
        });
      }
    }
  }

  Future<void> selectCamera({CameraDescription? cameraDescription}) async {
    if (Platform.isMacOS) {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? savedPath = prefs.getString('fakePhotosPath');
      if (savedPath != null) {
        await _loadFakePhotosFromPath(savedPath);
      }
      return;
    }

    try {
      if (_cameras.isEmpty) {
        return;
      }

      // Dispose existing controller before creating new one
      await controller?.dispose();

      selectedCameraDescription = cameraDescription ?? _cameras[0];
      controller = CameraController(
        selectedCameraDescription!,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      if (!mounted) {
        return;
      }

      await controller!.initialize();

      if (!mounted) {
        return;
      }

      await controller!.lockCaptureOrientation(DeviceOrientation.portraitUp);
      _minAvailableZoom = await controller!.getMinZoomLevel();
      _maxAvailableZoom = await controller!.getMaxZoomLevel();
      _currentZoom = _currentZoom.clamp(_minAvailableZoom, _maxAvailableZoom);

      if (mounted) {
        setState(() {});
      }
    } on CameraException catch (e) {
      switch (e.code) {
        case 'CameraAccessDenied':
          MyMessage.show('You have denied camera access.');
        case 'CameraAccessDeniedWithoutPrompt':
          // iOS only
          MyMessage.show('Please go to Settings app to enable camera access.');
        case 'CameraAccessRestricted':
          // iOS only
          MyMessage.show('Camera access is restricted.');
        case 'AudioAccessDenied':
          MyMessage.show('You have denied audio access.');
        case 'AudioAccessDeniedWithoutPrompt':
          // iOS only
          MyMessage.show('Please go to Settings app to enable audio access.');
        case 'AudioAccessRestricted':
          // iOS only
          MyMessage.show('Audio access is restricted.');
        default:
          MyMessage.show(e.toString());
      }
    }
  }

  Future<void> requestCameraPermission() async {
    final PermissionStatus currentStatus = await Permission.camera.status;
    if (currentStatus == PermissionStatus.granted) {
      return;
    }

    final PermissionStatus status = await Permission.camera.request();
    if (status != PermissionStatus.granted && mounted) {
      showDialog(
        context: context,
        builder: (final BuildContext context) => AlertDialog(
          title: const Text('Camera Permission Required'),
          content: const Text(
            'This app needs camera access to take photos. Please grant camera permission in Settings.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        ),
      );
    }
  }

  // Also add location permission request
  Future<void> requestLocationPermission() async {
    final PermissionStatus currentStatus = await Permission.locationWhenInUse.status;
    if (currentStatus == PermissionStatus.granted) {
      return;
    }

    final PermissionStatus status = await Permission.locationWhenInUse.request();
    if (status != PermissionStatus.granted && mounted) {
      showDialog(
        context: context,
        builder: (final BuildContext context) => AlertDialog(
          title: const Text('Location Permission Required'),
          content: const Text(
            'This app needs location access to tag photos with GPS coordinates.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        ),
      );
    }
  }

  Future<Position> _getCurrentPositionSafely() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw const PermissionDeniedException('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw const PermissionDeniedException(
        'Location permissions are permanently denied',
      );
    }

    return await Geolocator.getCurrentPosition();
  }

  @override
  Widget build(final BuildContext context) {
    return GradientScreen(child: _buildContent());
  }

  Widget _buildContent() {
    if (_cameras.isEmpty) {
      return working();
    }
    if (isCameraAvaiable()) {
      return Stack(
        children: <Widget>[
          // Full screen camera preview or image display
          _buildCameraLive(),

          // Controls at the bottom
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: _bottomControlLivePhoto(),
          ),
        ],
      );
    }
    return const Center(child: Text('No camera available'));
  }

  bool isCameraAvaiable() {
    return controller != null && controller!.value.isInitialized;
  }

  Widget _buildCameraLive() {
    if (Platform.isMacOS) {
      if (_fakePhotos.isEmpty) {
        // Show a button to let the user pick the folder
        return Center(
          child: ElevatedButton(
            onPressed: _loadFakePhotos,
            child: const Text('Select fakeCamera folder'),
          ),
        );
      }
    }

    // Additional safety check to prevent using disposed controller
    final CameraController? currentController = controller;
    if (currentController == null) {
      return const Center(child: Text('Camera not available'));
    }

    return Stack(
      children: <Widget>[
        // Full screen camera preview
        CameraPreview(currentController),
        // Remove flash overlay
        if (_showSprayEffect) const Center(child: AnimatedSprayEffect()),
      ],
    );
  }

  Future<Map<String, double>?> _getLatLngFromExif(String filePath) async {
    final Uint8List fileBytes = await File(filePath).readAsBytes();
    final Map<String, IfdTag> tags = await readExifFromBytes(fileBytes);

    if (tags.containsKey('GPS GPSLatitude') && tags.containsKey('GPS GPSLongitude')) {
      final IfdValues latValues = tags['GPS GPSLatitude']!.values;
      final IfdValues lonValues = tags['GPS GPSLongitude']!.values;
      final String latRef = tags['GPS GPSLatitudeRef']?.printable ?? 'N';
      final String lonRef = tags['GPS GPSLongitudeRef']?.printable ?? 'E';

      double convertToDegree(List<dynamic> values) {
        final double deg = (values[0] is Ratio) ? (values[0] as Ratio).toDouble() : values[0].toDouble();
        final double min = (values[1] is Ratio) ? (values[1] as Ratio).toDouble() : values[1].toDouble();
        final double sec = (values[2] is Ratio) ? (values[2] as Ratio).toDouble() : values[2].toDouble();
        return deg + (min / 60.0) + (sec / 3600.0);
      }

      double latitude = convertToDegree(List<dynamic>.from(latValues.toList()));
      double longitude = convertToDegree(List<dynamic>.from(lonValues.toList()));

      if (latRef == 'S') {
        latitude = -latitude;
      }
      if (lonRef == 'W') {
        longitude = -longitude;
      }

      return <String, double>{'latitude': latitude, 'longitude': longitude};
    }
    return null;
  }

  void _showSpray() {
    setState(() {
      _showSprayEffect = true;
    });
  }

  void _hideSpray() {
    setState(() {
      _showSprayEffect = false;
    });
  }

  void onTakePictureButtonPressed() async {
    _showSpray();

    double latitude = 0;
    double longitude = 0;
    final String address = ''; // Initialize address here
    lastLatitude = latitude;
    lastLongitude = longitude;
    lastAddress = address;

    XFile? file;

    if (Platform.isMacOS) {
      if (imageFile == null) {
        MyMessage.show('Please select a photo first.');
        return;
      }
      file = imageFile;

      // Get location from EXIF for macOS
      final Map<String, double>? exifLocation = await _getLatLngFromExif(imageFile!.path);
      if (exifLocation != null) {
        latitude = exifLocation['latitude']!;
        longitude = exifLocation['longitude']!;
      }
    } else {
      // Mobile platforms: get current position and take picture
      try {
        final Position position = await _getCurrentPositionSafely();
        file = await takePicture();
        if (file == null) {
          return;
        }

        latitude = position.latitude;
        longitude = position.longitude;
      } catch (e) {
        MyMessage.show('Location access denied. Using default coordinates.');
        file = await takePicture();
        if (file == null) {
          return;
        }
        // Use default coordinates if location is denied
        latitude = 0.0;
        longitude = 0.0;
      }
    }

    // Common processing for both platforms
    await _processImageAndLocation(file!, latitude, longitude);
    _hideSpray();
  }

  Future<void> _saveImageToCache(XFile file, double latitude, double longitude, String address) async {
    try {
      // Validate the image file before processing
      final File imageFile = File(file.path);
      if (!await imageFile.exists()) {
        throw Exception('Image file does not exist: ${file.path}');
      }

      final int fileSize = await imageFile.length();
      if (fileSize == 0) {
        throw Exception('Image file is empty: ${file.path}');
      }

      // Use the new MuralMetadataService to save the image and its comprehensive metadata
      await _muralMetadataService.saveImageAndMetadata(
        imageFile: imageFile,
        latitude: latitude,
        longitude: longitude,
        date: DateTime.now(),
        address: address, // Include address in Mural metadata
        approvalStatus: 'local', // Mark as local initially
      );
    } catch (e) {
      debugPrint('Error saving image to cache: $e');
      rethrow;
    }
  }

  Future<void> _processImageAndLocation(
    XFile file,
    double latitude,
    double longitude,
  ) async {
    // Get address from coordinates first
    final String address = await getAddressFromLatLng(latitude, longitude);

    // Save image with comprehensive metadata including address
    await _saveImageToCache(file, latitude, longitude, address);

    if (mounted && widget.onPhotoSubmitted != null) {
      // Trigger callback to switch to History tab with local selection
      widget.onPhotoSubmitted!();
    }
  }

  Widget _bottomControlLivePhoto() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        ShutterButton(
          onPressed: onTakePictureButtonPressed,
        ),
        lensSelection(),
      ],
    );
  }

  Widget lensSelection() {
    if (Platform.isMacOS) {
      return Container();
    }

    // Sort cameras by name
    final List<CameraDescription> sortedCameras = List<CameraDescription>.from(_cameras)
      ..sort((CameraDescription a, CameraDescription b) => getAppleZoomLabel(a).compareTo(getAppleZoomLabel(b)));

    final List<Widget> segments = <Widget>[];
    int selectedIndex = 0;

    for (int i = 0; i < sortedCameras.length; i++) {
      final CameraDescription cameraDescription = sortedCameras[i];

      segments.add(
        Transform.rotate(
          angle: widget.isLandscape ? (widget.rotationAngle > 0 ? math.pi / 2 : -math.pi / 2) : 0,
          child: Text(
            getAppleZoomLabel(cameraDescription),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: MyColors.buttonTextColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
      if (selectedCameraDescription == cameraDescription) {
        selectedIndex = i;
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ButtonsSelection(
        items: segments,
        selectedIndex: selectedIndex,
        onItemSelected: (int index) async {
          await selectCamera(cameraDescription: sortedCameras[index]);
        },
      ),
    );
  }

  String getAppleZoomLabel(CameraDescription cameraDescription) {
    final String name = cameraDescription.name;

    if (name.endsWith(':1')) {
      return '0.5x'; // Ultra-Wide
    } else if (name.endsWith(':0')) {
      return '1x'; // Main Wide
    } else if (name.endsWith(':2')) {
      return '2x'; // Telephoto (2x or 3x depending on model)
    } else if (name.endsWith(':5')) {
      return '0.5x'; // iPhone 15 Pro Max periscope
    } else {
      return 'Other';
    }
  }

  Future<XFile?> takePicture() async {
    if (controller == null || !controller!.value.isInitialized) {
      MyMessage.show('Error: Camera not ready.');
      return null;
    }

    try {
      return await controller!.takePicture();
    } catch (e) {
      MyMessage.show('Error taking picture: $e');
      return null;
    }
  }

  Future<File> saveToPermanentFolder(final XFile file) async {
    if (kIsWeb) {
      // On web, we can't save to permanent folder, so return the original file
      MyMessage.show('Picture captured (web mode)');
      return File(file.path);
    }
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String filename = file.name;
    final String destinationPath = p.join(appDir.path, filename);
    final File savedImage = await File(file.path).copy(destinationPath);
    MyMessage.show('Picture saved to $destinationPath');
    return savedImage;
  }
}

Future<Position?> getCurrentPositionMacOS() async {
  LocationPermission permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
    permission = await Geolocator.requestPermission();
    if (permission != LocationPermission.always && permission != LocationPermission.whileInUse) {
      return null;
    }
  }
  return await Geolocator.getCurrentPosition();
}

///
/// {
///   "place_id": 265735152,
///   "licence": "Data © OpenStreetMap contributors, ODbL 1.0. http://osm.org/copyright",
///   "osm_type": "way",
///   "osm_id": 119082823,
///   "lat": "41.2705942",
///   "lon": "-8.0817173",
///   "class": "highway",
///   "type": "residential",
///   "place_rank": 26,
///   "importance": 0.0533888281071183,
///   "addresstype": "road",
///   "name": "Rua Miguel Pinto Martins",
///   "display_name": "Rua Miguel Pinto Martins, Campo da Feira, Amarante (São Gonçalo), Madalena, Cepelos e Gatão, Amarante, Porto, 4600-090, Portugal",
///   "address": {
///     "road": "Rua Miguel Pinto Martins",
///     "neighbourhood": "Campo da Feira",
///     "city_district": "Amarante (São Gonçalo), Madalena, Cepelos e Gatão",
///     "city": "Amarante",
///     "county": "Porto",
///     "ISO3166-2-lvl6": "PT-13",
///     "postcode": "4600-090",
///     "country": "Portugal",
///     "country_code": "pt"
///   },
///   "boundingbox": [
///     "41.2703144",
///     "41.2715171",
///     "-8.0820856",
///     "-8.0815159"
///   ]
/// }
Future<String> getAddressFromLatLng(double latitude, double longitude) async {
  final Uri url = Uri.parse(
    'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1',
  );

  try {
    final http.Response response = await http.get(
      url,
      headers: <String, String>{
        'User-Agent': 'MuralApp/1.0 (<EMAIL>)', // Use a valid contact
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);

      if (data['address'] != null) {
        final Map<String, dynamic> address = data['address'];
        final String road = address['road'] ?? '';
        final String city = address['city'] ?? address['town'] ?? address['village'] ?? '';
        final String country = address['country'] ?? '';

        final List<String> parts = <String>[];
        if (road.isNotEmpty) {
          parts.add(road);
        }
        if (city.isNotEmpty) {
          parts.add(city);
        }
        if (country.isNotEmpty) {
          parts.add(country);
        }

        return parts.join(', ');
      }
    }
  } catch (e) {
    debugPrint('Error retrieving address: $e');
  }

  return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
}
