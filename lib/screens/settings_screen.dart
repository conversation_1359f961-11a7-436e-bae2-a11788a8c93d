import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:mural/models/mural.dart';
import 'package:mural/screens/signin_screen.dart';
import 'package:mural/services/auth_service.dart';
import 'package:mural/services/backend_sync_service.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_message.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final MuralMetadataService _xmpMetadataService = MuralMetadataService();

  @override
  Widget build(BuildContext context) {
    final AuthService authService = AuthService();

    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (BuildContext context, AsyncSnapshot<User?> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (!snapshot.hasData) {
          return const SignInScreen();
        }

        final User user = snapshot.data!;

        return Scaffold(
          appBar: AppBar(
            title: Text('Settings', style: Theme.of(context).textTheme.headlineSmall),
          ),
          body: GradientScreen(
            child: ListView(
              children: <Widget>[
                ListTile(
                  leading: const Icon(
                    Icons.person,
                  ),
                  title: const Text('Account'),
                  subtitle: Text(
                    user.email ?? 'No email',
                  ),
                ),
                const Divider(color: Colors.white30),
                ListTile(
                  leading: const Icon(Icons.logout),
                  title: const Text('Sign Out'),
                  onTap: () async {
                    await authService.signOut();
                  },
                ),
                const Divider(color: Colors.white30),
                if (kDebugMode)
                  ListTile(
                    leading: const Icon(Icons.spoke),
                    title: const Text('Test Message'),
                    onTap: () {
                      MyMessage.show('Hello');
                    },
                  ),
                if (kDebugMode) const Divider(color: Colors.white30),

                ListTile(
                  leading: const Icon(Icons.folder),
                  title: const Text('Stats'),
                  subtitle: FutureBuilder<Map<String, dynamic>>(
                    future: getMetadataAnalysis(),
                    builder: (BuildContext context, AsyncSnapshot<Map<String, dynamic>> snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Text('Loading stats...');
                      } else if (snapshot.hasError) {
                        return const Text('Error loading stats');
                      } else if (snapshot.hasData) {
                        final Map<String, dynamic> stats = snapshot.data!;
                        return Text(
                          'Total: ${stats['totalEntries'] ?? 0}, '
                          'GPS: ${stats['withGpsData'] ?? 0}, '
                          'Address: ${stats['withAddress'] ?? 0}, '
                          'Crop: ${stats['withCropData'] ?? 0}, '
                          'Missing: ${stats['missingMetadata'] ?? 0}',
                        );
                      } else {
                        return const Text('No stats available');
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Analyzes metadata completeness
  Future<Map<String, dynamic>> getMetadataAnalysis() async {
    final Map<String, dynamic> metadataStats = <String, dynamic>{};

    try {
      final List<Map<String, dynamic>> allMetadata = await _xmpMetadataService.getAllImageMetadata();
      debugPrint('Total metadata entries found: ${allMetadata.length}');

      int withGpsData = 0;
      int withAddress = 0;
      int withCropData = 0;
      int missingMetadata = 0;

      for (final Map<String, dynamic> metadata in allMetadata) {
        debugPrint('Processing metadata: $metadata');
        if (metadata['latitude'] != null && metadata['longitude'] != null) {
          withGpsData++;
        }
        if (metadata['address'] != null && metadata['address'].toString().isNotEmpty) {
          withAddress++;
        }
        if (metadata['cropCorners'] != null) {
          withCropData++;
        }
        if (metadata['latitude'] == null || metadata['longitude'] == null || metadata['dateTaken'] == null) {
          missingMetadata++;
        }
      }

      metadataStats['totalEntries'] = allMetadata.length;
      metadataStats['withGpsData'] = withGpsData;
      metadataStats['withAddress'] = withAddress;
      metadataStats['withCropData'] = withCropData;
      metadataStats['missingMetadata'] = missingMetadata;
    } catch (e) {
      debugPrint('Error analyzing metadata: $e');
      metadataStats['withGpsData'] = 0;
      metadataStats['withAddress'] = 0;
      metadataStats['withCropData'] = 0;
      metadataStats['missingMetadata'] = 0;
    }

    return metadataStats;
  }

  /// Gets sync statistics with backend
  Future<Map<String, dynamic>> getSyncStatistics() async {
    final Map<String, dynamic> syncStats = <String, dynamic>{};

    try {
      final BackendSyncService syncService = BackendSyncService();
      final Map<String, String> syncStatus = await syncService.getSyncStatus();
      debugPrint('Raw sync status from service: $syncStatus');

      int pendingUpload = 0;
      int uploaded = 0;
      int approved = 0;
      int rejected = 0;
      int localOnly = 0;

      for (final String status in syncStatus.values) {
        switch (status) {
          case 'pending':
            pendingUpload++;
            break;
          case 'uploaded':
            uploaded++;
            break;
          case 'approved':
            approved++;
            break;
          case 'rejected':
            rejected++;
            break;
          case 'local':
          default:
            localOnly++;
            break;
        }
      }

      syncStats['pendingUpload'] = pendingUpload;
      syncStats['uploaded'] = uploaded;
      syncStats['approved'] = approved;
      syncStats['rejected'] = rejected;
      syncStats['localOnly'] = localOnly;

      // Get last sync time (this would need to be stored somewhere)
      // For now, we'll leave it null
      syncStats['lastSyncTime'] = null;

      // Get last photo time
      final List<Map<String, dynamic>> allMetadata = await _xmpMetadataService.getAllImageMetadata();
      if (allMetadata.isNotEmpty) {
        DateTime? lastPhotoDate;
        for (final Map<String, dynamic> metadata in allMetadata) {
          final DateTime? dateTaken = metadata['dateTaken'] as DateTime?;
          if (dateTaken != null && (lastPhotoDate == null || dateTaken.isAfter(lastPhotoDate))) {
            lastPhotoDate = dateTaken;
          }
        }
        if (lastPhotoDate != null) {
          syncStats['lastPhotoTime'] = formatDateTime(lastPhotoDate);
        }
      }
    } catch (e) {
      debugPrint('Error getting sync statistics: $e');
      syncStats['pendingUpload'] = 0;
      syncStats['uploaded'] = 0;
      syncStats['approved'] = 0;
      syncStats['rejected'] = 0;
      syncStats['localOnly'] = 0;
    }

    return syncStats;
  }

  /// Validates that an image file is not corrupted
  Future<bool> validateImageFile(File imageFile) async {
    try {
      if (!await imageFile.exists()) {
        return false;
      }

      final int fileSize = await imageFile.length();
      if (fileSize == 0) {
        return false;
      }

      // Try to decode the image to verify it's valid
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? image = img.decodeImage(imageBytes);
      return image != null;
    } catch (e) {
      return false;
    }
  }

  /// Formats bytes into human-readable format
  String formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    }
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    }
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Formats DateTime into readable format
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Action: rebuild metadata entries for JPGs missing metadata by reading EXIF
  Future<void> rebuildMissingMetadataFromJpgs() async {
    try {
      final String storagePath = await _xmpMetadataService.getImageCacheDirPath();
      final Directory storageDir = Directory(storagePath);
      if (!await storageDir.exists()) {
        MyMessage.show('No storage directory found');
        return;
      }
      final List<FileSystemEntity> allFiles = await storageDir.list().toList();
      final List<File> imageFiles = allFiles.whereType<File>().where((File f) => f.path.endsWith('.jpg')).toList();
      final Set<String> jpgHashes = imageFiles.map((File f) => f.path.split('/').last.replaceAll('.jpg', '')).toSet();

      final Set<String> metadataHashes = await _xmpMetadataService.getAllMetadataHashes();
      final List<String> missing = jpgHashes.difference(metadataHashes).toList();

      int rebuilt = 0;
      for (final String h in missing) {
        final MuralMetadata? meta = await _xmpMetadataService.recreateMetadataFromJpgExif(h);
        if (meta != null) {
          await _xmpMetadataService.updateMetadata(
            imageHash: h,
            latitude: meta.latitude,
            longitude: meta.longitude,
            address: meta.address,
            dateTaken: meta.dateTaken,
          );
          rebuilt++;
        }
      }

      MyMessage.show('Rebuilt $rebuilt metadata entr${rebuilt == 1 ? 'y' : 'ies'} from EXIF');
    } catch (e) {
      debugPrint('Error rebuilding metadata: $e');
      MyMessage.show('Error: $e');
    }
  }

  /// Builds a statistics section with title and rows
  Widget buildStatSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  /// Builds a statistics row with label and value
  Widget buildStatRow(String label, String value, [bool asRow = true]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: asRow
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  flex: 3,
                  child: Text(
                    label,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    value,
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.deepOrangeAccent),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(label, style: const TextStyle(fontSize: 14)),
                Text(
                  value,
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Colors.deepOrangeAccent),
                ),
              ],
            ),
    );
  }

  /// Performs sync with backend
  Future<void> performSync(BuildContext context) async {
    try {
      final BackendSyncService syncService = BackendSyncService();
      await syncService.syncMetadataUpdates();
      MyMessage.show('Sync completed successfully');
    } catch (e) {
      debugPrint('Error during sync: $e');
      MyMessage.show('Sync failed: $e');
    }
  }
}
