import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/screens/photo_screen/photo_screen.dart';
import 'package:mural/screens/signin_screen.dart';
import 'package:mural/services/photo_service.dart';
import 'package:mural/widgets/buttons_selection.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/photo_list_item.dart';
import 'package:mural/widgets/responsive_photo_list.dart';
import 'package:mural/widgets/working.dart';

// Wrapper class to make QueryDocumentSnapshot compatible with PhotoDocument
class QueryDocumentWrapper implements PhotoDocument {
  QueryDocumentWrapper(this._doc);
  final QueryDocumentSnapshot<Object?> _doc;

  @override
  Map<String, dynamic> data() => _doc.data() as Map<String, dynamic>;

  @override
  String get id => _doc.id;
}

// Enum to manage the UI state for the filter buttons
enum ReviewStatusFilter { pending, approved, rejected }

class ReviewScreen extends StatefulWidget {
  const ReviewScreen({super.key});

  @override
  State<ReviewScreen> createState() => _ReviewScreenState();
}

class _ReviewScreenState extends State<ReviewScreen> {
  ReviewStatusFilter _selectedFilter = ReviewStatusFilter.pending;
  final PhotoService _photoService = PhotoService();

  /// Returns the correct Firestore query based on the selected filter.
  Query<Map<String, dynamic>> _getQueryForFilter(ReviewStatusFilter filter) {
    final CollectionReference<Map<String, dynamic>> collection = FirebaseFirestore.instance.collection('murals');
    switch (filter) {
      case ReviewStatusFilter.pending:
        return collection.where('status', isEqualTo: 'pending');
      case ReviewStatusFilter.approved:
        return collection.where('status', isEqualTo: 'approved');
      case ReviewStatusFilter.rejected:
        return collection.where('status', isEqualTo: 'rejected');
    }
  }

  /// Updates the status of a mural document in Firestore.
  void _updateStatus(String docId, String newStatus) {
    FirebaseFirestore.instance.collection('murals').doc(docId).update(<Object, Object?>{'status': newStatus});
  }

  @override
  Widget build(final BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (BuildContext context, AsyncSnapshot<User?> userSnapshot) {
        if (userSnapshot.connectionState == ConnectionState.waiting) {
          return working();
        }
        if (!userSnapshot.hasData) {
          return const SignInScreen();
        }

        return Scaffold(
          body: GradientScreen(
            child: Stack(
              children: <Widget>[
                Positioned.fill(
                  child: StreamBuilder<QuerySnapshot<dynamic>>(
                    stream: _getQueryForFilter(_selectedFilter).snapshots(),
                    builder: (BuildContext context, AsyncSnapshot<QuerySnapshot<Object?>> muralSnapshot) {
                      if (muralSnapshot.connectionState == ConnectionState.waiting) {
                        return working();
                      }
                      if (muralSnapshot.hasError) {
                        return Center(child: Text('Error: ${muralSnapshot.error}'));
                      }
                      if (!muralSnapshot.hasData || muralSnapshot.data!.docs.isEmpty) {
                        return const Center(child: Text('No images in this category.'));
                      }

                      final List<QueryDocumentSnapshot<Object?>> docs = muralSnapshot.data!.docs;
                      return ResponsivePhotoList(
                        itemCount: docs.length,
                        itemBuilder: (BuildContext context, int index) => _buildItem(docs, index),
                      );
                    },
                  ),
                ),
                Positioned(
                  bottom: 85,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: ButtonsSelection(
                      items: const <Widget>[
                        Text('Pending'),
                        Text('Approved'),
                        Text('Rejected'),
                      ],
                      selectedIndex: _selectedFilter.index,
                      onItemSelected: (int index) {
                        setState(() {
                          _selectedFilter = ReviewStatusFilter.values[index];
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildItem(List<QueryDocumentSnapshot<Object?>> docs, int index) {
    final QueryDocumentSnapshot<Object?> doc = docs[index];
    final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    final String docId = doc.id;

    final String title = data['address'] ?? 'Unknown location';
    final String subtitle = ''; //data['status'] ?? 'Unknown status';
    final String imageUrl = data['imageUrl'];
    final double? latitude = data['latitude'];
    final double? longitude = data['longitude'];

    // Create Photo object from data
    final Photo photo = _photoService.createPhotoFromData(data);

    Widget? customImage;
    if (_selectedFilter == ReviewStatusFilter.pending) {
      customImage = Stack(
        children: <Widget>[
          Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Icon(Icons.error, color: Colors.red, size: 50),
                    SizedBox(height: 8),
                    Text(
                      'Could not load image',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
          Positioned.fill(
            child: Center(
              child: SizedBox(
                height: 40,
                child: Row(
                  spacing: 16,
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    MyButton(
                      onPressed: () => _updateStatus(docId, 'approved'),
                      child: const Icon(Icons.check_circle, color: Colors.green),
                    ),
                    MyButton(
                      onPressed: () => _updateStatus(docId, 'rejected'),
                      child: const Icon(Icons.cancel, color: Colors.red),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    }

    return PhotoListItem(
      photo: photo,
      title: title,
      subtitle: subtitle,
      date: data['timestamp'] != null ? (data['timestamp'] as Timestamp).toDate().toString().split(' ')[0] : null,
      customImage: customImage,
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute<dynamic>(
          builder: (_) => PhotoScreen(
            imageUrl: imageUrl,
            address: title,
            latitude: latitude,
            longitude: longitude,
            docId: docId,
            photoList: docs.map((QueryDocumentSnapshot<Object?> doc) => QueryDocumentWrapper(doc)).toList(),
            currentIndex: index,
            isReviewMode: true,
            onPhotoDeleted: () {
              // The StreamBuilder will automatically refresh when Firestore data changes
              // This callback ensures proper state management
            },
          ),
        ),
      ),
    );
  }
}
