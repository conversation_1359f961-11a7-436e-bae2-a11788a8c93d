import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/my_message.dart';

class AddressScreen extends StatefulWidget {
  const AddressScreen({
    super.key,
    required this.initialAddress,
    this.latitude,
    this.longitude,
  });

  final String? initialAddress;
  final double? latitude;
  final double? longitude;

  @override
  State<AddressScreen> createState() => _AddressScreenState();
}

class _AddressScreenState extends State<AddressScreen> {
  late final TextEditingController _addressController;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _addressController = TextEditingController(text: widget.initialAddress);
    _addressController.addListener(() {
      setState(() {
        _hasChanges = _addressController.text != widget.initialAddress;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: KeyboardListener(
        focusNode: FocusNode()..requestFocus(),
        onKeyEvent: (KeyEvent event) {
          if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.escape) {
            Navigator.of(context).pop();
          }
        },
        child: GradientScreen(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 600),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  spacing: 16,
                  mainAxisAlignment: MainAxisAlignment.center,
                  // crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    Row(
                      spacing: 8,
                      children: <Widget>[
                        MyButton.icon(
                          icon: Icons.arrow_back,
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                        const Text(
                          'Update Address',
                          style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 60),
                    TextField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelStyle: TextStyle(color: Colors.black87),
                        fillColor: Colors.white,
                        filled: true,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black54),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                      ),
                      style: const TextStyle(color: Colors.black),
                      maxLines: 3,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      spacing: 16,
                      children: <Widget>[
                        Expanded(
                          child: MyButton.iconText(
                            onPressed: widget.latitude != null && widget.longitude != null
                                ? () async {
                                    final String newAddress = await getAddressFromLatLng(
                                      widget.latitude!,
                                      widget.longitude!,
                                    );
                                    _addressController.text = newAddress;
                                  }
                                : null,
                            icon: Icons.location_pin,
                            text:
                                'From Photo GPS ${widget.latitude?.toStringAsFixed(4)}, ${widget.longitude?.toStringAsFixed(4)}',
                          ),
                        ),
                        Expanded(
                          child: MyButton.iconText(
                            onPressed: () async {
                              try {
                                final Position? position = await getCurrentPositionMacOS();
                                if (position != null) {
                                  final String newAddress = await getAddressFromLatLng(
                                    position.latitude,
                                    position.longitude,
                                  );
                                  _addressController.text = newAddress;
                                } else {
                                  MyMessage.show('Could not get current location.');
                                }
                              } catch (e) {
                                MyMessage.show('Error getting location: $e');
                              }
                            },
                            icon: Icons.my_location,
                            text: 'From Current Location',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    MyButton.text(
                      isEnabled: _hasChanges,
                      onPressed: () {
                        Navigator.of(context).pop(_addressController.text);
                      },
                      maxWidth: 100,
                      isPrimary: true,
                      text: 'Save',
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

Future<Position?> getCurrentPositionMacOS() async {
  LocationPermission permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
    permission = await Geolocator.requestPermission();
    if (permission != LocationPermission.always && permission != LocationPermission.whileInUse) {
      return null;
    }
  }
  return await Geolocator.getCurrentPosition();
}

Future<String> getAddressFromLatLng(double latitude, double longitude) async {
  final Uri url = Uri.parse(
    'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1',
  );

  try {
    final http.Response response = await http.get(
      url,
      headers: <String, String>{
        'User-Agent': 'MuralApp/1.0 (<EMAIL>)', // Use a valid contact
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);

      if (data['address'] != null) {
        final Map<String, dynamic> address = data['address'];
        final String houseNumber = address['house_number'] ?? '';
        final String road = address['road'] ?? '';
        final String city = address['city'] ?? address['town'] ?? address['village'] ?? '';
        final String country = address['country'] ?? '';

        final List<String> parts = <String>[];
        if (houseNumber.isNotEmpty && road.isNotEmpty) {
          parts.add('$houseNumber $road');
        } else if (road.isNotEmpty) {
          parts.add(road);
        }
        if (city.isNotEmpty) {
          parts.add(city);
        }
        if (country.isNotEmpty) {
          parts.add(country);
        }

        return parts.join(', ');
      }
    }
  } catch (e) {
    debugPrint('Error retrieving address: $e');
  }

  return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
}
