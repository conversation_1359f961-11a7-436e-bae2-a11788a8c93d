import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mural/services/auth_service.dart';
import 'package:mural/widgets/my_message.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  bool _isLoading = false;
  final AuthService _authService = AuthService();

  Future<void> _signIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final UserCredential? result = await _authService.signInWithGoogle();
      if (result == null && mounted) {
        setState(() {
          _isLoading = false;
        });
        String message = 'Sign-in was cancelled or failed. Please try again.';
        if (_authService.lastErrorCode == 'keychain-error') {
          message =
              'macOS Keychain error. Please ensure the app is signed with a valid Apple Developer Team and Keychain Sharing is enabled in Signing & Capabilities. Then clean/rebuild and try again.';
        }
        MyMessage.show(message);
      }
      // If successful, the auth state change will handle navigation
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        String errorMessage = 'Sign-in failed. Please try again.';
        if (kIsWeb) {
          errorMessage += ' Make sure popups are enabled for this site.';
        }
        MyMessage.show(errorMessage);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              spacing: 8,
              children: <Widget>[
                const Icon(
                  Icons.photo_camera,
                  size: 80,
                  color: Colors.deepOrange,
                ),
                const Text(
                  'Discover Street Art',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Text(
                  'Find, collect, and share amazing murals/graffiti in your city.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      spacing: 6,
                      children: <Widget>[
                        Icon(Icons.security, color: Colors.green),
                        Text(
                          'Why sign in?',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '• Save your mural discoveries\n• Sync across devices\n• Share with friends\n• Track your collection',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                _isLoading
                    ? const CircularProgressIndicator()
                    : ElevatedButton.icon(
                        onPressed: _signIn,
                        icon: Container(
                          padding: const EdgeInsets.all(3),
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            color: Colors.white,
                          ),
                          child: SvgPicture.asset(
                            'assets/google_logo.svg',
                            width: 20,
                            height: 20,
                          ),
                        ),
                        label: const Text('Sign in with Google'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                        ),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
