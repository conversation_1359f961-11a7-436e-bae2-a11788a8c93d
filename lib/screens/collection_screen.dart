import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:mural/constants/status_constants.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/screens/photo_screen/photo_screen.dart';
import 'package:mural/screens/signin_screen.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/services/photo_service.dart';
import 'package:mural/widgets/buttons_selection.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/photo_list_item.dart';
import 'package:mural/widgets/responsive_photo_list.dart';
import 'package:mural/widgets/working.dart';
import 'package:path_provider/path_provider.dart';

// Wrapper class to make Map data compatible with PhotoScreen
class DocumentWrapper implements PhotoDocument {
  DocumentWrapper(this._data);
  final Map<String, dynamic> _data;

  @override
  Map<String, dynamic> data() => _data;

  @override
  String get id => _data['id'] ?? '';
}

class CollectionScreen extends StatefulWidget {
  const CollectionScreen({
    super.key,
    this.initialSelectedStatus = 'local',
    this.onSwitchToCamera,
  });

  final String initialSelectedStatus;
  final VoidCallback? onSwitchToCamera;

  @override
  State<CollectionScreen> createState() => _CollectionScreenState();
}

class _CollectionScreenState extends State<CollectionScreen> {
  late String _selectedStatus;
  List<Map<String, dynamic>> _remoteDocs = <Map<String, dynamic>>[];
  List<Map<String, dynamic>> _localDocs = <Map<String, dynamic>>[];
  bool _isLoadingLocal = false;
  User? _currentUser;

  final MuralMetadataService _muralMetadataService = MuralMetadataService();
  final PhotoService _photoService = PhotoService();

  @override
  void initState() {
    super.initState();
    _selectedStatus = kIsWeb ? StatusConstants.pending : widget.initialSelectedStatus;
    if (!kIsWeb) {
      _loadLocalImages();
    }
    _listenToAuth();
  }

  void _listenToAuth() {
    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      if (mounted) {
        setState(() {
          _currentUser = user;
        });
        if (user != null) {
          _readCache().then((_) => _loadRemoteDocs());
        } else {
          setState(() {
            _remoteDocs = <Map<String, dynamic>>[];
          });
        }
      }
    });
  }

  void _loadRemoteDocs() {
    if (_currentUser == null) {
      return;
    }

    FirebaseFirestore.instance.collection('murals').where('userId', isEqualTo: _currentUser!.uid).snapshots().listen((
      QuerySnapshot<dynamic> snapshot,
    ) {
      if (mounted) {
        final List<Map<String, dynamic>> docs = snapshot.docs.map((QueryDocumentSnapshot<dynamic> doc) {
          final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          return data;
        }).toList();

        setState(() {
          _remoteDocs = docs;
        });
        _writeCache(snapshot.docs);
      }
    });
  }

  Future<String> _getAddressFromLatLng(double latitude, double longitude) async {
    try {
      final List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final Placemark place = placemarks[0];
        return '${place.street}, ${place.locality}, ${place.country}';
      }
    } catch (e) {
      debugPrint('Error retrieving address: $e');
    }
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  Future<void> _loadLocalImages() async {
    if (_isLoadingLocal) {
      // Prevent multiple simultaneous loads
      return;
    }
    setState(() {
      _isLoadingLocal = true;
    });
    try {
      final List<Map<String, dynamic>> allMetadata = await _muralMetadataService.getAllImageMetadata();
      final List<Map<String, dynamic>> localDocs = <Map<String, dynamic>>[];

      for (final Map<String, dynamic> metadata in allMetadata) {
        final String imageHash = metadata['imageHash'];
        final String imageCacheDirPath = await _muralMetadataService.getImageCacheDirPath();
        final String imagePath = '$imageCacheDirPath/$imageHash.jpg';
        final double? latitude = metadata['latitude'];
        final double? longitude = metadata['longitude'];
        final DateTime date = metadata['dateTaken'] != null
            ? DateTime.tryParse(metadata['dateTaken'] as String) ?? DateTime.now()
            : DateTime.now();

        // Use address from Mural metadata if available, otherwise calculate it
        String address = metadata['address'] as String? ?? 'Unknown location';
        if (address == 'Unknown location' && latitude != null && longitude != null) {
          address = await _getAddressFromLatLng(latitude, longitude);
        }

        localDocs.add(<String, dynamic>{
          'imageUrl': imagePath,
          'address': address,
          'latitude': latitude,
          'longitude': longitude,
          'timestamp': date.millisecondsSinceEpoch,
          'status':
              metadata['approvalStatus'] ?? 'local', // Use approval status from Mural metadata or default to 'local'
          'id': metadata['imageHash'], // Use imageHash as the ID

          'cropCorners': metadata['cropCorners'], // Include crop corners for potential future use
        });
      }

      // Convert timestamps back to Timestamp objects for consistency
      final List<Map<String, dynamic>> processedLocalDocs = localDocs.map((Map<String, dynamic> doc) {
        if (doc['timestamp'] is int) {
          doc['timestamp'] = Timestamp.fromMillisecondsSinceEpoch(doc['timestamp'] as int);
        }
        return doc;
      }).toList();

      setState(() {
        _localDocs = processedLocalDocs;
        _isLoadingLocal = false;
      });
    } on FileSystemException catch (e) {
      debugPrint('Error loading local images: $e');
      setState(() {
        _isLoadingLocal = false;
      });
    }
  }

  Future<File> _getCacheFile() async {
    final Directory directory = await getApplicationDocumentsDirectory();
    return File('${directory.path}/history_cache.json');
  }

  Future<void> _readCache() async {
    try {
      final File file = await _getCacheFile();
      if (await file.exists()) {
        final String contents = await file.readAsString();
        final List<dynamic> decoded = jsonDecode(contents);
        final List<Map<String, dynamic>> cachedDocs = decoded.map((dynamic d) {
          final Map<String, dynamic> doc = d as Map<String, dynamic>;
          if (doc.containsKey('timestamp') && doc['timestamp'] != null) {
            if (doc['timestamp'] is int) {
              doc['timestamp'] = Timestamp.fromMillisecondsSinceEpoch(doc['timestamp'] as int);
            }
          }
          return doc;
        }).toList();

        if (mounted) {
          setState(() {
            _remoteDocs = cachedDocs;
          });
        }
      }
    } catch (e) {
      debugPrint('Error reading cache: $e');
    }
  }

  Future<void> _writeCache(List<QueryDocumentSnapshot<dynamic>> docs) async {
    try {
      final File file = await _getCacheFile();
      final List<Map<String, dynamic>> data = docs.map((QueryDocumentSnapshot<dynamic> doc) {
        final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        if (data.containsKey('timestamp') && data['timestamp'] != null) {
          data['timestamp'] = (data['timestamp'] as Timestamp).millisecondsSinceEpoch;
        }
        return data;
      }).toList();
      await file.writeAsString(jsonEncode(data));
    } catch (e) {
      debugPrint('Error writing cache: $e');
    }
  }

  // ignore: unused_element
  String _getStatusText(final Map<String, dynamic> data) {
    switch (data['status']) {
      case StatusConstants.approved:
        return StatusConstants.approvedDisplay;
      case StatusConstants.rejected:
        return StatusConstants.rejectedDisplay;
      case StatusConstants.pending:
      default:
        return StatusConstants.pendingDisplay;
    }
  }

  @override
  Widget build(final BuildContext context) {
    if (_currentUser == null && _selectedStatus != 'local') {
      return const SignInScreen();
    }

    final List<Map<String, dynamic>> currentDocs = _selectedStatus == 'local' ? _localDocs : _remoteDocs;

    return Scaffold(
      body: GradientScreen(
        child: Stack(
          children: <Widget>[
            Positioned.fill(
              child: _buildBody(currentDocs),
            ),
            if (_localDocs.isNotEmpty || _remoteDocs.isNotEmpty)
              Positioned(
                bottom: 85,
                left: 0,
                right: 0,
                child: _listSelection(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(List<Map<String, dynamic>> docs) {
    if (_selectedStatus == 'local' && _isLoadingLocal) {
      return working();
    }
    final List<Map<String, dynamic>> filteredDocs = docs.where((Map<String, dynamic> doc) {
      final String docStatus = doc.containsKey('status') ? doc['status'] : StatusConstants.pending;

      if (_selectedStatus == 'local') {
        return docStatus == 'local';
      } else if (_selectedStatus == StatusConstants.pending) {
        return docStatus == StatusConstants.pending;
      } else {
        return docStatus == _selectedStatus;
      }
    }).toList();

    // Sort the filtered documents by timestamp in descending order, handling nulls
    filteredDocs.sort((Map<String, dynamic> a, Map<String, dynamic> b) {
      final Timestamp? timestampA = a['timestamp'] as Timestamp?;
      final Timestamp? timestampB = b['timestamp'] as Timestamp?;

      if (timestampA == null && timestampB == null) {
        return 0;
      }
      if (timestampA == null) {
        return 1;
      } // nulls last
      if (timestampB == null) {
        return -1;
      }
      return timestampB.compareTo(timestampA);
    });

    if (filteredDocs.isEmpty) {
      return _buildEmptyState();
    }

    return ResponsivePhotoList(
      itemCount: filteredDocs.length,
      itemBuilder: (BuildContext context, int index) => _buildItem(filteredDocs, index),
    );
  }

  Widget _listSelection() {
    final int localCount = _localDocs.length;
    final int pendingCount = _remoteDocs.where((Map<String, dynamic> d) {
      final String status = d.containsKey('status') ? d['status'] : StatusConstants.pending;
      return status == StatusConstants.pending;
    }).length;
    final int approvedCount = _remoteDocs.where((Map<String, dynamic> d) {
      final String status = d.containsKey('status') ? d['status'] : StatusConstants.pending;
      return status == StatusConstants.approved;
    }).length;
    final int rejectedCount = _remoteDocs.where((Map<String, dynamic> d) {
      final String status = d.containsKey('status') ? d['status'] : StatusConstants.pending;
      return status == StatusConstants.rejected;
    }).length;

    final List<Widget> items = <Widget>[
      if (!kIsWeb) Text('Local\n$localCount', textAlign: TextAlign.center),
      Text('${StatusConstants.pendingDisplay}\n$pendingCount', textAlign: TextAlign.center),
      Text('${StatusConstants.approvedDisplay}\n$approvedCount', textAlign: TextAlign.center),
      Text('${StatusConstants.rejectedDisplay}\n$rejectedCount', textAlign: TextAlign.center),
    ];

    return ButtonsSelection(
      items: items,
      selectedIndex: kIsWeb
          ? switch (_selectedStatus) {
              StatusConstants.pending => 0,
              StatusConstants.approved => 1,
              StatusConstants.rejected => 2,
              _ => 0,
            }
          : switch (_selectedStatus) {
              'local' => 0,
              StatusConstants.pending => 1,
              StatusConstants.approved => 2,
              StatusConstants.rejected => 3,
              _ => 0,
            },
      onItemSelected: (int index) {
        setState(() {
          _selectedStatus = kIsWeb
              ? switch (index) {
                  0 => StatusConstants.pending,
                  1 => StatusConstants.approved,
                  2 => StatusConstants.rejected,
                  _ => StatusConstants.pending,
                }
              : switch (index) {
                  0 => 'local',
                  1 => StatusConstants.pending,
                  2 => StatusConstants.approved,
                  3 => StatusConstants.rejected,
                  _ => 'local',
                };
        });
      },
    );
  }

  Widget _buildEmptyState() {
    final bool hasNoPhotosAtAll = _localDocs.isEmpty && _remoteDocs.isEmpty;

    if (hasNoPhotosAtAll) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            spacing: 16,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.camera_alt_outlined,
                size: 80,
                color: Colors.white70,
              ),
              const Text(
                'Welcome to Mural!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const Text(
                'Discover and collect city murals and graffiti. Your photos will appear here in your Collection.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
                softWrap: true,
              ),
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(Icons.location_on, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      'Remember to enable GPS so others can find and visit the murals you discover!',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      softWrap: true,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => widget.onSwitchToCamera?.call(),
                icon: const Icon(Icons.camera_alt),
                label: const Text('Take Your First Photo'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return const Center(
      child: Text(
        'No photos found for this status.',
        style: TextStyle(color: Colors.white70),
      ),
    );
  }

  Widget _buildItem(List<Map<String, dynamic>> docs, int index) {
    final Map<String, dynamic> data = docs[index];
    final Photo photo = _photoService.createPhotoFromData(data);

    return PhotoListItem(
      photo: photo,
      title: data['address'] ?? 'Unknown location',
      subtitle: '', //_getStatusText(data),
      date: data['timestamp'] != null ? (data['timestamp'] as Timestamp).toDate().toString().split(' ')[0] : null,
      onTap: () => _openFullscreen(
        context,
        photo.effectiveImageSource, // Pass the full path for local, or URL for remote
        data['address'],
        data['latitude'],
        data['longitude'],
        data['id'] ?? '',
        () => _loadLocalImages(), // Callback to refresh local images
        docs, // Pass the filtered photo list
        index, // Pass the current index
      ),
    );
  }

  void _openFullscreen(
    BuildContext context,
    String imageIdentifier, // Renamed from imageUrl
    String? address,
    double? latitude,
    double? longitude,
    String docId,
    VoidCallback? onPhotoSubmitted,
    List<Map<String, dynamic>> photoList,
    int currentIndex,
  ) async {
    // Convert Map data to DocumentWrapper for PhotoScreen compatibility
    final List<DocumentWrapper> photoWrappers = photoList
        .map((Map<String, dynamic> data) => DocumentWrapper(data))
        .toList();

    await Navigator.push(
      context,
      MaterialPageRoute<dynamic>(
        builder: (_) => PhotoScreen(
          imageUrl: imageIdentifier, // Pass imageIdentifier (which is imageHash for local)
          address: address,
          latitude: latitude,
          longitude: longitude,
          docId: docId,
          isLocal: _selectedStatus == 'local',
          onPhotoSubmitted: onPhotoSubmitted,
          onPhotoDeleted: () {
            // Refresh the appropriate list when photo is deleted
            if (_selectedStatus == 'local') {
              _loadLocalImages();
            }
          },
          photoList: photoWrappers,
          currentIndex: currentIndex,
        ),
      ),
    );

    if (_selectedStatus == 'local') {
      _loadLocalImages();
    }
  }
}
