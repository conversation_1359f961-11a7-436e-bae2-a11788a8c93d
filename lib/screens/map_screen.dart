import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:latlong2/latlong.dart';
import 'package:mural/constants/status_constants.dart';
import 'package:mural/screens/photo_screen/photo_screen.dart';
import 'package:mural/widgets/gradient_screen.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key, this.tileProvider, this.rotationAngle = 0.0});
  final TileProvider? tileProvider;
  final double rotationAngle;

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final MapController _mapController = MapController();
  final LatLng _center = const LatLng(45.521563, -122.677433);

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  List<Marker> _buildMarkers(List<QueryDocumentSnapshot<dynamic>> docs) {
    final List<Marker> markers = <Marker>[];
    for (final QueryDocumentSnapshot<Object?> doc in docs) {
      final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      if (data['latitude'] != null && data['longitude'] != null) {
        markers.add(
          Marker(
            width: 80.0,
            height: 80.0,
            point: LatLng(data['latitude'], data['longitude']),
            child: GestureDetector(
              onTap: () => _showPhotoDialog(context, data, doc.id),
              child: Center(
                child: Transform.rotate(
                  angle: widget.rotationAngle,
                  child: SvgPicture.asset(
                    'assets/camera_icon.svg',
                    // 'assets/spray_can_icon.svg',
                    width: 35.0,
                    height: 35.0,
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    return markers;
  }

  Future<void> _getCurrentLocation() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        // If permission is still denied, fall back to IP-based location
        throw const PermissionDeniedException('Permission denied by user.');
      }
      final Position position = await Geolocator.getCurrentPosition();
      _mapController.move(LatLng(position.latitude, position.longitude), 15.0);
    } catch (e) {
      // If precise location fails or is denied, use IP-based fallback
      final LatLng? ipLocation = await _getIPBasedLocation();
      if (ipLocation != null) {
        _mapController.move(
          ipLocation,
          10.0,
        ); // Zoom out for less accurate location
      }
    }
  }

  Future<LatLng?> _getIPBasedLocation() async {
    try {
      final http.Response response = await http.get(Uri.parse('http://ip-api.com/json'));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['status'] == 'success') {
          return LatLng(data['lat'], data['lon']);
        }
      }
    } catch (e) {
      // Could not get IP-based location
    }
    return null;
  }

  void _showPhotoDialog(BuildContext context, Map<String, dynamic> data, String docId) {
    Navigator.push(
      context,
      MaterialPageRoute<void>(
        // ignore: always_specify_types
        builder: (context) => PhotoScreen(
          imageUrl: data['imageUrl'],
          address: data['address'],
          latitude: data['latitude']?.toDouble(),
          longitude: data['longitude']?.toDouble(),
          docId: docId,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientScreen(
        child: StreamBuilder<QuerySnapshot<dynamic>>(
          stream: FirebaseFirestore.instance
              .collection('murals')
              .where('status', isEqualTo: StatusConstants.approved)
              .snapshots(),
          builder: (BuildContext context, AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            final List<Marker> markers = snapshot.hasData ? _buildMarkers(snapshot.data!.docs) : <Marker>[];
            return FlutterMap(
              mapController: _mapController,
              options: MapOptions(initialCenter: _center, initialZoom: 11.0),
              children: <Widget>[
                TileLayer(
                  urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                  userAgentPackageName: 'com.vteam.mural',
                  tileProvider: widget.tileProvider ?? NetworkTileProvider(),
                ),
                MarkerLayer(markers: markers),
              ],
            );
          },
        ),
      ),
    );
  }
}
