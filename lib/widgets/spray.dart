// Spray effect widget
import 'dart:math' as math;

import 'package:flutter/material.dart';

class AnimatedSprayEffect extends StatefulWidget {
  const AnimatedSprayEffect({super.key});

  @override
  State<AnimatedSprayEffect> createState() => _AnimatedSprayEffectState();
}

class _AnimatedSprayEffectState extends State<AnimatedSprayEffect> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    )..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return CustomPaint(
          size: Size(constraints.maxWidth, constraints.maxHeight),
          painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(_controller),
        );
      },
    );
  }
}

class SprayPainter extends CustomPainter {
  SprayPainter(this.animation) : super(repaint: animation);
  final Animation<double> animation;

  @override
  void paint(Canvas canvas, Size size) {
    // Staring position: 3/4 from the bottom center
    final Offset nozzle = Offset(size.width / 2, size.height * 0.8);

    final double progress = animation.value;
    final double nozzleRadius = 1;

    final math.Random random = math.Random(42);

    final double targetCenterY = size.height / 2;
    final double maxVerticalDistance = (nozzle.dy - targetCenterY).abs() + (size.height - targetCenterY);

    final double screenCoverageHeight = maxVerticalDistance * progress;
    final double sprayRadius = (size.width / 2) * progress;

    for (int i = 0; i < 1000; i++) {
      final double t = random.nextDouble();
      final double dx = (random.nextDouble() - 0.5) * sprayRadius * 2;
      final double dy = -t * screenCoverageHeight;

      final Offset sprayOffset = nozzle.translate(dx, dy);

      final Color color = i % 2 == 0 ? Colors.orange.withAlpha(200) : Colors.white.withAlpha(200);

      final Paint paint = Paint()
        ..color = color
        ..strokeCap = StrokeCap.round
        ..strokeWidth = nozzleRadius * (1.0 - progress) + 2;

      canvas.drawCircle(sprayOffset, nozzleRadius * (1.0 - progress) + 2, paint);
    }
  }

  @override
  bool shouldRepaint(covariant SprayPainter oldDelegate) => true;
}
