import 'dart:math';

import 'package:flutter/material.dart';

class RectangleAdjuster extends StatefulWidget {
  const RectangleAdjuster({
    super.key,
    required this.initialCorners,
    required this.onCornersChanged,
    this.onAutoDetect,
  });
  final List<Offset> initialCorners;
  final ValueChanged<List<Offset>> onCornersChanged;
  final VoidCallback? onAutoDetect;

  @override
  RectangleAdjusterState createState() => RectangleAdjusterState();
}

class RectangleAdjusterState extends State<RectangleAdjuster> {
  late List<Offset> corners;

  @override
  void initState() {
    super.initState();
    corners = widget.initialCorners;
  }

  @override
  void didUpdateWidget(covariant final RectangleAdjuster oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialCorners != oldWidget.initialCorners) {
      setState(() {
        corners = widget.initialCorners;
      });
    }
  }

  int? _draggingCornerIndex;
  int? _draggingLineIndex;

  void _startDragging(final DragStartDetails details) {
    const double handleRadius = 20.0;
    final Offset position = details.localPosition;
    _draggingCornerIndex = null;
    _draggingLineIndex = null;

    for (int i = 0; i < corners.length; i++) {
      if ((corners[i] - position).distance <= handleRadius) {
        setState(() {
          _draggingCornerIndex = i;
        });
        return;
      }
    }

    const double lineHitboxWidth = 20.0;
    for (int i = 0; i < corners.length; i++) {
      final Offset p1 = corners[i];
      final Offset p2 = corners[(i + 1) % 4];
      final double distance = _distanceToSegment(position, p1, p2);
      if (distance <= lineHitboxWidth) {
        setState(() {
          _draggingLineIndex = i;
        });
        return;
      }
    }
  }

  double _distanceToSegment(
    final Offset p,
    final Offset p1,
    final Offset p2,
  ) {
    final double l2 = (p2 - p1).distanceSquared;
    if (l2 == 0.0) {
      return (p - p1).distance;
    }
    final double t = ((p - p1).dx * (p2 - p1).dx + (p - p1).dy * (p2 - p1).dy) / l2;
    final double clampedT = t.clamp(0.0, 1.0);
    final Offset projection = p1 + (p2 - p1) * clampedT;
    return (p - projection).distance;
  }

  void _updateDragging(final DragUpdateDetails details) {
    if (_draggingCornerIndex != null) {
      setState(() {
        corners[_draggingCornerIndex!] = details.localPosition;
        _notifyChanged();
      });
    } else if (_draggingLineIndex != null) {
      setState(() {
        final int i = _draggingLineIndex!;
        final Offset p1 = corners[i];
        final Offset p2 = corners[(i + 1) % 4];

        // Determine if the line is mostly horizontal or vertical
        if ((p1.dx - p2.dx).abs() > (p1.dy - p2.dy).abs()) {
          // Horizontal line, move vertically
          final double dy = details.delta.dy;
          corners[i] = corners[i].translate(0, dy);
          corners[(i + 1) % 4] = corners[(i + 1) % 4].translate(0, dy);
        } else {
          // Vertical line, move horizontally
          final double dx = details.delta.dx;
          corners[i] = corners[i].translate(dx, 0);
          corners[(i + 1) % 4] = corners[(i + 1) % 4].translate(dx, 0);
        }
        _notifyChanged();
      });
    }
  }

  void _stopDragging() {
    setState(() {
      _draggingCornerIndex = null;
      _draggingLineIndex = null;
    });
  }

  void _notifyChanged() {
    widget.onCornersChanged(corners);
  }

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: <Widget>[
        GestureDetector(
          onPanStart: _startDragging,
          onPanUpdate: _updateDragging,
          onPanEnd: (final _) => _stopDragging(),
          child: CustomPaint(
            size: Size.infinite,
            painter: DraggableRectanglePainter(corners),
          ),
        ),
        if (widget.onAutoDetect != null)
          Positioned(
            left: 0,
            right: 0,
            bottom: 20,
            child: Center(
              child: ElevatedButton.icon(
                onPressed: widget.onAutoDetect,
                icon: const Icon(Icons.auto_fix_high, size: 18),
                label: const Text('Detect'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

class DraggableRectanglePainter extends CustomPainter {
  DraggableRectanglePainter(this.corners);
  final List<Offset> corners;
  static const double handleRadius = 20.0;

  @override
  void paint(final Canvas canvas, final Size size) {
    // Draw corner handles
    final Paint handlePaint = Paint();
    handlePaint.color = Colors.black54;
    handlePaint.style = PaintingStyle.fill;

    // White border
    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    for (final Offset corner in corners) {
      canvas.drawCircle(corner, handleRadius, handlePaint);
      canvas.drawCircle(corner, handleRadius, borderPaint);
    }

    // Draw mid-point handles
    for (int i = 0; i < corners.length; i++) {
      final Offset p1 = corners[i];
      final Offset p2 = corners[(i + 1) % 4];
      final Offset midPoint = Offset((p1.dx + p2.dx) / 2, (p1.dy + p2.dy) / 2);
      canvas.drawCircle(midPoint, handleRadius, handlePaint);
      canvas.drawCircle(midPoint, handleRadius, borderPaint);
    }

    final Paint linePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    // Draw dashed lines
    for (int i = 0; i < 4; i++) {
      final Offset start = corners[i];
      final Offset end = corners[(i + 1) % 4];
      _drawDashedLine(canvas, start, end, linePaint);
    }
  }

  void _drawDashedLine(
    final Canvas canvas,
    final Offset start,
    final Offset end,
    final Paint paint,
  ) {
    const double dashWidth = 20.0;
    const double dashSpace = 0.0;
    final double totalLength = (end - start).distance;
    final Offset direction = (end - start) / totalLength;

    double distance = 0;
    bool isWhite = true;
    while (distance < totalLength) {
      paint.color = (isWhite ? Colors.white : Colors.black);
      final Offset currentStart = start + direction * distance;
      final Offset currentEnd = start + direction * min(distance + dashWidth, totalLength);
      canvas.drawLine(currentStart, currentEnd, paint);
      distance += dashWidth + dashSpace;
      isWhite = !isWhite;
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) => true;
}
