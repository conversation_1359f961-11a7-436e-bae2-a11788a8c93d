import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// You can move these to a shared colors.dart file if you want
const Color kSprayCanLidColor = Colors.white;
const Color kSprayCanLidPressedColor = Colors.orange;
const Color kSprayCanRimColor = Colors.orange;
const Color kSprayCanSplatterColor = Color(0xFFFFB74D); // orange.shade300
const Color kSprayCanNozzleColor = Colors.grey;

class ShutterButton extends StatefulWidget {
  const ShutterButton({
    super.key,
    required this.onPressed,
  });

  final VoidCallback onPressed;

  @override
  State<ShutterButton> createState() => _ShutterButtonState();
}

class _ShutterButtonState extends State<ShutterButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        HapticFeedback.mediumImpact();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        widget.onPressed();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        width: _isPressed ? 120 : 100,
        height: _isPressed ? 120 : 100,
        child: CustomPaint(
          painter: _PaintCanWithNozzlePainter(isPressed: _isPressed),
        ),
      ),
    );
  }
}

class _PaintCanWithNozzlePainter extends CustomPainter {
  _PaintCanWithNozzlePainter({required this.isPressed});
  final bool isPressed;

  @override
  void paint(Canvas canvas, Size size) {
    final Offset center = size.center(Offset.zero);
    final double lidRadius = min(size.width, size.height) / 2.5;
    final int splatterCount = 24;

    // Nozzle (circle above the lid)
    final double nozzleRadius = 12.0;
    final Offset nozzleCenter = Offset(center.dx, center.dy - lidRadius - nozzleRadius + 12);

    // Nozzle tip (smaller circle, changes color when pressed)
    final Paint tipPaint = Paint()
      ..color = isPressed ? kSprayCanLidPressedColor : kSprayCanLidColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(nozzleCenter, nozzleRadius, tipPaint);

    // Splatter blobs around the lid
    final Paint splatterPaint = Paint()
      ..color = kSprayCanSplatterColor
      ..style = PaintingStyle.fill;
    final double splatterRadius = 6.0;
    final double splatterDistanceFromCenter = lidRadius - splatterRadius / 2;
    for (int i = 0; i < splatterCount; i++) {
      final double angle = (2 * pi / splatterCount) * i;
      final double dx = center.dx + cos(angle) * splatterDistanceFromCenter;
      final double dy = center.dy + sin(angle) * splatterDistanceFromCenter;
      canvas.drawCircle(Offset(dx, dy), splatterRadius, splatterPaint);
    }

    // Lid
    final Paint lidPaint = Paint()
      ..color = isPressed ? kSprayCanLidPressedColor : kSprayCanLidColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, lidRadius, lidPaint);

    // Rim
    final Paint rimPaint = Paint()
      ..color = kSprayCanRimColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    canvas.drawCircle(center, lidRadius, rimPaint);
  }

  @override
  bool shouldRepaint(covariant _PaintCanWithNozzlePainter oldDelegate) {
    return oldDelegate.isPressed != isPressed;
  }
}
