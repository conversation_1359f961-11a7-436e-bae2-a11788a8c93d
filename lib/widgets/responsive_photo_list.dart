import 'package:flutter/material.dart';

class ResponsivePhotoList extends StatelessWidget {
  const ResponsivePhotoList({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
  });

  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 8,
        bottom: MediaQuery.of(context).padding.bottom + 138,
        left: 8,
        right: 8,
      ),
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final double itemWidth = constraints.maxWidth > 1200
              ? 600
              : constraints.maxWidth > 600
              ? (constraints.maxWidth - 16) / 2
              : constraints.maxWidth;
          return Wrap(
            alignment: WrapAlignment.center,
            spacing: 8,
            runSpacing: 8,
            children: List<Widget>.generate(
              itemCount,
              (int index) => SizedBox(
                width: itemWidth,
                child: itemBuilder(context, index),
              ),
            ),
          );
        },
      ),
    );
  }
}
