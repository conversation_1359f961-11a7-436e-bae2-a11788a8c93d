import 'package:flutter/material.dart';

import 'package:mural/models/photo.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/photo_widget.dart';

class PhotoListItem extends StatelessWidget {
  const PhotoListItem({
    super.key,
    required this.photo,
    required this.onTap,
    this.title,
    this.subtitle,
    this.date,
    this.customImage,
  });

  final Photo photo;
  final VoidCallback onTap;
  final String? title;
  final String? subtitle;
  final String? date;
  final Widget? customImage;

  @override
  Widget build(BuildContext context) {
    final String displayTitle = title ?? photo.displayTitle;
    final String displaySubtitle = subtitle ?? photo.statusText;
    final String? displayDate = date ?? _formatDate(photo.metadata.dateTaken);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        decoration: const BoxDecoration(
          color: Colors.black45,
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: <Widget>[
              AspectRatio(
                aspectRatio: 16 / 9,
                child: customImage ?? PhotoWidget(photo: photo, fit: BoxFit.cover),
              ),
              if (displayDate != null)
                Positioned(
                  top: 8,
                  left: 8,
                  child: MyButton(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    borderRadius: 4,
                    child: Text(displayDate, style: const TextStyle(fontSize: 10)),
                  ),
                ),
              if (displaySubtitle.isNotEmpty)
                Positioned(
                  top: 8,
                  right: 8,
                  child: MyButton(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    borderRadius: 4,
                    child: Text(displaySubtitle, style: const TextStyle(fontSize: 10)),
                  ),
                ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: MyButton(
                  padding: const EdgeInsets.all(8),
                  borderRadius: 0,
                  child: Text(displayTitle, style: const TextStyle(fontSize: 9)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String? _formatDate(DateTime? date) {
    if (date == null) {
      return null;
    }
    return '${date.day}/${date.month}/${date.year}';
  }
}
