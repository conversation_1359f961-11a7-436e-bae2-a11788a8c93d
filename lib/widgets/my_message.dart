import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mural/navigator_key.dart';

class MyMessage {
  static OverlayEntry? _overlayEntry;

  static void show(String message) {
    _showBanner(message, Colors.black, Colors.orange);
  }

  static void showError(String message) {
    _showBanner(message, Colors.red, Colors.redAccent);
  }

  static void _showBanner(String message, Color backgroundColor, Color surfaceTintColor) {
    // Remove any existing banner
    hide();

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: SafeArea(
            top: true,
            bottom: false,
            left: false,
            right: false,
            child: Material(
              elevation: 6,
              child: MaterialBanner(
                padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                content: GestureDetector(
                  onLongPress: () {
                    Clipboard.setData(ClipboardData(text: message));
                    _showCopiedTooltip();
                  },
                  child: Text(message, style: const TextStyle(color: Colors.white)),
                ),
                actions: <Widget>[
                  const IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: hide,
                  ),
                ],
                backgroundColor: backgroundColor,
                surfaceTintColor: surfaceTintColor,
                shadowColor: Colors.black,
                dividerColor: Colors.transparent,
              ),
            ),
          ),
        );
      },
    );

    // Insert the banner into the overlay
    navigatorKey.currentState?.overlay?.insert(_overlayEntry!);
  }

  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  static void _showCopiedTooltip() {
    late OverlayEntry tooltipEntry;
    tooltipEntry = OverlayEntry(
      builder: (BuildContext context) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 3000),
          tween: Tween<double>(begin: 1.0, end: 0.0),
          builder: (BuildContext context, double opacity, Widget? child) {
            return Positioned(
              top: 50,
              left: 0,
              right: 0,
              child: Center(
                child: Opacity(
                  opacity: opacity,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      'Copied',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        decoration: TextDecoration.none,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
          onEnd: () {
            tooltipEntry.remove();
          },
        );
      },
    );

    navigatorKey.currentState?.overlay?.insert(tooltipEntry);
  }
}
