import 'dart:math' as math;

import 'package:flutter/material.dart';

Widget adaptiveLandscape({
  required bool isLandscape,
  required double angle,
  required Widget child,
  Duration duration = const Duration(milliseconds: 500),
}) {
  final double targetAngle = isLandscape ? (angle > 0 ? math.pi / 2 : -math.pi / 2) : 0;

  return TweenAnimationBuilder<double>(
    tween: Tween<double>(begin: 0, end: targetAngle),
    duration: duration,
    builder: (BuildContext context, double angle, Widget? child) {
      return Transform.rotate(angle: angle, child: child);
    },
    child: child,
  );
}
