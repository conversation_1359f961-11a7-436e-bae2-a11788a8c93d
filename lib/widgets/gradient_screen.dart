import 'package:flutter/material.dart';
import 'package:mural/constants/my_colors.dart';

class GradientScreen extends StatelessWidget {
  const GradientScreen({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: <Color>[MyColors.gradientStart, MyColors.gradientEnd],
        ),
      ),
      child: child,
    );
  }
}
