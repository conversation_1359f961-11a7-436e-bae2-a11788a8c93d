import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/widgets/my_button.dart';
import 'package:mural/widgets/working.dart';

class PhotoWidget extends StatefulWidget {
  const PhotoWidget({
    super.key,
    required this.photo,
    this.fit = BoxFit.contain,
    this.showOverlays = false,
    this.onTap,
  });

  final Photo photo;
  final BoxFit fit;
  final bool showOverlays;
  final VoidCallback? onTap;

  @override
  State<PhotoWidget> createState() => _PhotoWidgetState();
}

class _PhotoWidgetState extends State<PhotoWidget> {
  late Future<String?> _imageUrlFuture;

  @override
  void initState() {
    super.initState();
    _imageUrlFuture = widget.photo.getImageUrl();
  }

  @override
  void didUpdateWidget(PhotoWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.photo.imageHash != widget.photo.imageHash) {
      _imageUrlFuture = widget.photo.getImageUrl();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child = FutureBuilder<String?>(
      future: _imageUrlFuture,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: working(),
          );
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data == null) {
          return _buildErrorDisplay(snapshot.error);
        }

        final String imageUrl = snapshot.data!;

        if (kIsWeb) {
          return Image.network(
            imageUrl,
            fit: widget.fit,
            loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
              if (loadingProgress == null) {
                return child;
              }
              return Center(
                child: working(),
              );
            },
            errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
              debugPrint('Image.network error for $imageUrl: $error');
              return _buildErrorDisplay(error);
            },
          );
        } else {
          return Image.file(
            File(imageUrl),
            fit: widget.fit,
            errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
              debugPrint('Image.file error for $imageUrl: $error');
              _cleanupCorruptedImage(File(imageUrl));
              return _buildErrorDisplay(error);
            },
          );
        }
      },
    );

    if (widget.onTap != null) {
      child = GestureDetector(
        onTap: widget.onTap,
        child: child,
      );
    }

    return child;
  }

  Widget _buildErrorDisplay(Object? error) {
    return Container(
      color: Colors.grey[900],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.broken_image,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          Text(
            'Image Error',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.photo.fileName,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.showOverlays) ...<Widget>[
            const SizedBox(height: 8),
            _buildMetadataOverlay(),
          ],
        ],
      ),
    );
  }

  Widget _buildMetadataOverlay() {
    final MuralMetadata metadata = widget.photo.metadata;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: <Widget>[
          if (metadata.address != null)
            MyButton(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              borderRadius: 4,
              child: Text(
                metadata.address!,
                style: const TextStyle(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ),
          const SizedBox(height: 4),
          if (metadata.dateTaken != null)
            MyButton(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              borderRadius: 4,
              child: Text(
                _formatDate(metadata.dateTaken!),
                style: const TextStyle(fontSize: 10),
              ),
            ),
          const SizedBox(height: 4),
          MyButton(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            borderRadius: 4,
            child: Text(
              'Status: ${widget.photo.statusText.toUpperCase()}',
              style: const TextStyle(fontSize: 10),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _cleanupCorruptedImage(File imageFile) async {
    try {
      if (await imageFile.exists()) {
        await imageFile.delete();
        debugPrint('Cleaned up corrupted image: ${imageFile.path}');
      }
    } catch (e) {
      debugPrint('Error cleaning up corrupted image: $e');
    }
  }
}
