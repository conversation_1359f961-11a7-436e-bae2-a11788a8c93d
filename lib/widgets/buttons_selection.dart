import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:mural/constants/my_colors.dart';

class ButtonsSelection extends StatefulWidget {
  const ButtonsSelection({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemSelected,
  });
  final List<Widget> items;
  final int selectedIndex;
  final void Function(int index) onItemSelected;

  @override
  State<ButtonsSelection> createState() => _ButtonsSelectionState();
}

class _ButtonsSelectionState extends State<ButtonsSelection> {
  @override
  Widget build(BuildContext context) {
    final int itemCount = widget.items.length;

    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: MyColors.blurSigma, sigmaY: MyColors.blurSigma),
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: <Color>[
                    MyColors.backgroundStart,
                    MyColors.backgroundEnd,
                  ],
                ),
                border: Border.all(color: MyColors.buttonBorderColor),
                boxShadow: <BoxShadow>[
                  const BoxShadow(
                    color: Colors.black54,
                    blurRadius: 10,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  final double width = constraints.maxWidth;
                  final double segmentWidth = width / itemCount;

                  return Stack(
                    children: <Widget>[
                      // Selected Segment Background
                      AnimatedAlign(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeOut,
                        alignment: Alignment(
                          -1.0 + 2.0 * widget.selectedIndex / (itemCount - 1),
                          0,
                        ),
                        child: Container(
                          width: segmentWidth,
                          height: 40,
                          margin: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: LinearGradient(
                              colors: <Color>[
                                Colors.orange,
                                MyColors.primaryButtonColor,
                                MyColors.primaryButtonColor.withAlpha(200),
                                MyColors.primaryButtonColor,
                                Colors.orange,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: <BoxShadow>[
                              BoxShadow(
                                color: MyColors.buttonBorderColorSelected,
                                blurRadius: 2,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Segments
                      Row(
                        children: List<Widget>.generate(itemCount, (int index) {
                          final bool isSelected = index == widget.selectedIndex;
                          return Expanded(
                            child: GestureDetector(
                              onTap: () => widget.onItemSelected(index),
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                alignment: Alignment.center,
                                height: 48,
                                child: AnimatedDefaultTextStyle(
                                  duration: const Duration(milliseconds: 200),
                                  style: TextStyle(
                                    color: isSelected ? MyColors.buttonTextColorSelected : MyColors.buttonTextColor,
                                    fontWeight: FontWeight.w900,
                                    fontSize: 10,
                                  ),
                                  child: widget.items[index],
                                ),
                              ),
                            ),
                          );
                        }),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
