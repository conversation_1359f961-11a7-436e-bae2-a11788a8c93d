import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:mural/constants/my_colors.dart';

class MyButton extends StatefulWidget {
  const MyButton({
    super.key,
    required this.child,
    this.onPressed,
    this.isEnabled = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    this.borderRadius = 12,
    this.isPrimary = false,
    this.isSelected = false,
    this.maxWidth,
  }) : icon = null,
       text = null;

  const MyButton.iconText({
    super.key,
    required this.icon,
    required this.text,
    this.onPressed,
    this.isEnabled = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    this.borderRadius = 12,
    this.isPrimary = false,
    this.isSelected = false,
    this.maxWidth,
  }) : child = null;

  const MyButton.icon({
    super.key,
    required this.icon,
    this.onPressed,
    this.isEnabled = true,
    this.padding = const EdgeInsets.all(12),
    this.borderRadius = 50,
    this.isPrimary = false,
    this.isSelected = false,
    this.maxWidth,
  }) : child = null,
       text = null;

  const MyButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.isEnabled = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    this.borderRadius = 12,
    this.isPrimary = false,
    this.isSelected = false,
    this.maxWidth,
  }) : child = null,
       icon = null;

  final Widget? child;
  final IconData? icon;
  final String? text;
  final bool isPrimary;
  final bool isSelected;
  final bool isEnabled;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final double? maxWidth;

  @override
  State<MyButton> createState() => _MyButtonState();
}

class _MyButtonState extends State<MyButton> with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  bool _isPressed = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildContent() {
    if (widget.child != null) {
      return widget.child!;
    }

    if (widget.icon != null && widget.text != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(widget.icon, color: MyColors.buttonTextColor),
          const SizedBox(width: 4),
          Text(widget.text!),
        ],
      );
    }

    if (widget.icon != null) {
      return Icon(widget.icon, color: MyColors.buttonTextColor);
    }

    if (widget.text != null) {
      return Text(widget.text!);
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final bool isEnabled = widget.isEnabled;
    final bool isHighlighted = widget.isPrimary || widget.isSelected;
    final double opacity = isEnabled ? 1.0 : 0.5;

    return MouseRegion(
      onEnter: isEnabled ? (_) => setState(() => _isHovered = true) : null,
      onExit: isEnabled ? (_) => setState(() => _isHovered = false) : null,
      child: GestureDetector(
        onTapDown: isEnabled
            ? (_) {
                setState(() => _isPressed = true);
                _animationController.forward();
              }
            : null,
        onTapUp: isEnabled
            ? (_) {
                setState(() => _isPressed = false);
                _animationController.reverse();
              }
            : null,
        onTapCancel: isEnabled
            ? () {
                setState(() => _isPressed = false);
                _animationController.reverse();
              }
            : null,
        onTap: isEnabled ? widget.onPressed : null,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (BuildContext context, Widget? child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: opacity,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  child: BackdropFilter(
                    filter: _isHovered
                        ? ImageFilter.blur(sigmaX: MyColors.blurSigma, sigmaY: MyColors.blurSigma)
                        : ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                    child: Container(
                      padding: widget.padding,
                      constraints: widget.icon != null && widget.child == null && widget.text == null
                          ? const BoxConstraints(minWidth: 48, minHeight: 48)
                          : BoxConstraints(
                              maxWidth: widget.maxWidth ?? double.infinity,
                            ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(widget.borderRadius),
                        gradient: isHighlighted
                            ? LinearGradient(
                                colors: <Color>[
                                  Colors.orange,
                                  MyColors.primaryButtonColor,
                                  MyColors.primaryButtonColor.withAlpha(200),
                                  MyColors.primaryButtonColor,
                                  Colors.orange,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              )
                            : LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: <Color>[
                                  MyColors.backgroundStart,
                                  MyColors.backgroundEnd,
                                ],
                              ),
                        border: Border.all(
                          color: widget.onPressed != null && (_isHovered || _isPressed)
                              ? MyColors.buttonBorderColor.withAlpha(255)
                              : MyColors.buttonBorderColor,
                        ),
                        boxShadow: <BoxShadow>[
                          BoxShadow(
                            color: Colors.white.withAlpha(_isHovered ? 80 : 50),
                            blurRadius: _isHovered ? 3 : 2,
                            spreadRadius: 1,
                          ),
                          BoxShadow(
                            color: Colors.black54,
                            blurRadius: _isHovered ? 12 : 10,
                            spreadRadius: _isHovered ? 6 : 5,
                          ),
                        ],
                      ),
                      child: Center(
                        child: DefaultTextStyle(
                          style: TextStyle(
                            color: MyColors.buttonTextColor,
                            fontSize: 12,
                            letterSpacing: -0.5,
                          ),
                          child: _buildContent(),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
