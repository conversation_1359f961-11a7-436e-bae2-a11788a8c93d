import 'package:flutter/material.dart';

class MyColors {
  static const MaterialColor themeSwatch = Colors.deepOrange;

  static const Color gradientStart = Color.fromARGB(255, 255, 196, 0);
  static const Color gradientEnd = Color.fromARGB(236, 178, 69, 1);

  static Color primaryButtonColor = Colors.deepOrange;
  static Color secondaryButtonColor = const Color(0xFF424242);
  static Color buttonTextColor = Colors.white;
  static Color buttonTextColorSelected = Colors.white;
  static Color buttonBorderColor = const Color.fromARGB(127, 255, 255, 255);
  static Color buttonBorderColorSelected = Colors.white70;

  static Color backgroundStart = Colors.grey.withAlpha(40);
  static Color backgroundEnd = Colors.white.withAlpha(20);

  static double blurSigma = 5.0; // blur intensity
}
