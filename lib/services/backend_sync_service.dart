import 'dart:io';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:image/image.dart' as img;
import 'package:mural/models/mural.dart';

import 'mural_metadata_service.dart';
import 'photo_screen_service.dart';

/// Enhanced backend sync service for Photo + XMP system
class BackendSyncService {
  static const String _muralsCollection = 'murals';
  static const String _storageBasePath = 'murals';
  static const String _xmpVersion = '1.0';

  final MuralMetadataService _xmpService = MuralMetadataService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Submit photo with comprehensive metadata and deduplication
  Future<String?> submitPhotoWithMetadata({
    required String imageHash,
    required MuralMetadata metadata,
    bool checkDuplicates = true,
  }) async {
    final User? user = FirebaseAuth.instance.currentUser;
    if (user == null || user.uid.isEmpty) {
      throw Exception('User not authenticated or user ID is empty');
    }

    try {
      // Check for duplicates if requested
      if (checkDuplicates) {
        final String? existingDocId = await _checkForDuplicate(imageHash);
        if (existingDocId != null) {
          debugPrint('Duplicate photo found: $existingDocId');
          return await _handleDuplicatePhoto(existingDocId, metadata);
        }
      }

      // Get image file path
      final String imageCacheDirPath = await _xmpService.getImageCacheDirPath();
      final String imagePath = '$imageCacheDirPath/$imageHash.jpg';
      final File imageFile = File(imagePath);

      if (!await imageFile.exists()) {
        throw Exception('Image file not found: $imagePath');
      }

      // Upload both image and XMP files to Firebase Storage
      final Map<String, String> uploadUrls = await _uploadImageAndXmp(imageFile, user.uid, imageHash);

      // Save comprehensive metadata to Firestore
      final DocumentReference<Object?> docRef = await _saveMetadataToFirestore(
        user.uid,
        imageHash,
        uploadUrls['imageUrl']!,
        uploadUrls['xmpUrl']!,
        metadata,
      );

      // Update local XMP with cloud reference
      await _xmpService.updateMetadata(
        imageHash: imageHash,
        approvalStatus: 'pending',
        userId: user.uid,
      );
      return docRef.id;
    } catch (e) {
      debugPrint('Error submitting photo: $e');
      rethrow;
    }
  }

  /// Check if a photo with the same hash already exists
  Future<String?> _checkForDuplicate(String imageHash) async {
    try {
      final QuerySnapshot<Map<String, dynamic>> query = await _firestore
          .collection(_muralsCollection)
          .where('imageHash', isEqualTo: imageHash)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return query.docs.first.id;
      }
      return null;
    } catch (e) {
      debugPrint('Error checking for duplicates: $e');
      return null;
    }
  }

  /// Handle duplicate photo submission
  Future<String> _handleDuplicatePhoto(String existingDocId, MuralMetadata metadata) async {
    // For now, just update the existing document with additional metadata
    // In the future, this could merge metadata from multiple sources
    try {
      await _firestore.collection(_muralsCollection).doc(existingDocId).update(<String, dynamic>{
        'lastUpdated': FieldValue.serverTimestamp(),
        'duplicateSubmissions': FieldValue.increment(1),
        // Could add contributor information here
      });

      debugPrint('Updated existing photo document: $existingDocId');
      return existingDocId;
    } catch (e) {
      debugPrint('Error handling duplicate photo: $e');
      rethrow;
    }
  }

  /// Upload both image and XMP files to Firebase Storage
  Future<Map<String, String>> _uploadImageAndXmp(File imageFile, String userId, String imageHash) async {
    try {
      // Read and convert image to JPG
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Could not decode image');
      }

      final Uint8List jpgBytes = Uint8List.fromList(img.encodeJpg(image, quality: 85));

      // Upload image to Firebase Storage
      final String imageFileName = '$imageHash.jpg';
      final Reference imageStorageRef = _storage.ref().child('$_storageBasePath/$userId/$imageFileName');

      await imageStorageRef.putData(jpgBytes).timeout(const Duration(minutes: 2));
      final String imageDownloadUrl = await imageStorageRef.getDownloadURL();
      debugPrint('Image uploaded successfully: $imageDownloadUrl');

      // Build and upload XMP content from stored metadata (Hive-backed)
      String xmpDownloadUrl = '';
      try {
        final MuralMetadataService xmpService = MuralMetadataService();
        final MuralMetadata? meta = await xmpService.getPhotoMetadata(imageHash);
        if (meta != null) {
          final PhotoScreenService pss = PhotoScreenService();
          final String xmpContent = await pss.createMuralMetadataContent(meta);
          final Uint8List xmpBytes = Uint8List.fromList(xmpContent.codeUnits);
          final String xmpFileName = '$imageHash.xmp';
          final Reference xmpStorageRef = _storage.ref().child('$_storageBasePath/$userId/$xmpFileName');
          await xmpStorageRef.putData(xmpBytes).timeout(const Duration(minutes: 2));
          xmpDownloadUrl = await xmpStorageRef.getDownloadURL();
          debugPrint('XMP content uploaded successfully: $xmpDownloadUrl');
        } else {
          debugPrint('Warning: Metadata not found for image hash: $imageHash');
        }
      } catch (e) {
        debugPrint('Warning: Failed to create/upload XMP content for $imageHash: $e');
      }

      return <String, String>{
        'imageUrl': imageDownloadUrl,
        'xmpUrl': xmpDownloadUrl,
      };
    } catch (e) {
      debugPrint('Error uploading image and XMP: $e');
      rethrow;
    }
  }

  /// Save comprehensive metadata to Firestore
  Future<DocumentReference<Object?>> _saveMetadataToFirestore(
    String userId,
    String imageHash,
    String downloadUrl,
    String xmpUrl,
    MuralMetadata metadata,
  ) async {
    // Validate required fields
    if (userId.isEmpty) {
      throw Exception('User ID cannot be empty');
    }
    if (imageHash.isEmpty) {
      throw Exception('Image hash cannot be empty');
    }
    if (downloadUrl.isEmpty) {
      throw Exception('Download URL cannot be empty');
    }

    try {
      final Map<String, dynamic> firestoreData = <String, dynamic>{
        // Core identification
        'userId': userId,
        'imageUrl': downloadUrl,
        'xmpUrl': xmpUrl,
        'imageHash': imageHash,

        // Location data
        'latitude': metadata.latitude,
        'longitude': metadata.longitude,
        'address': metadata.address,

        // Temporal data
        'timestamp': metadata.dateTaken != null
            ? Timestamp.fromDate(metadata.dateTaken!)
            : FieldValue.serverTimestamp(),
        'dateTaken': metadata.dateTaken != null
            ? Timestamp.fromDate(metadata.dateTaken!)
            : FieldValue.serverTimestamp(),
        'uploadedAt': FieldValue.serverTimestamp(),

        // Processing metadata
        'cropCorners': metadata.cropCorners
            ?.map(
              (Offset offset) => <String, double>{
                'x': offset.dx,
                'y': offset.dy,
              },
            )
            .toList(),

        // Workflow status
        'status': 'pending',
        'approvalStatus': 'pending',

        // Schema evolution
        'xmpVersion': _xmpVersion,
        'metadataSource': 'xmp',

        // Additional tracking
        'duplicateSubmissions': 0,
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      final DocumentReference<Map<String, dynamic>> docRef = await _firestore
          .collection(_muralsCollection)
          .add(firestoreData);
      return docRef;
    } catch (e) {
      debugPrint('Error saving metadata to Firestore: $e');
      rethrow;
    }
  }

  /// Download approved photos and their metadata
  Future<List<Map<String, dynamic>>> getApprovedPhotos({
    int limit = 50,
    DocumentSnapshot<Object?>? startAfter,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection(_muralsCollection)
          .where('status', isEqualTo: 'approved')
          .orderBy('uploadedAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();

      return snapshot.docs.map((QueryDocumentSnapshot<Object?> doc) {
        final Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('Error getting approved photos: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Sync local metadata with cloud updates
  Future<void> syncMetadataUpdates() async {
    try {
      // Get all local photos
      final List<Map<String, dynamic>> localPhotos = await _xmpService.getAllImageMetadata();

      for (final Map<String, dynamic> localPhoto in localPhotos) {
        final String imageHash = localPhoto['imageHash'];

        // Check if this photo exists in the cloud
        final QuerySnapshot<Map<String, dynamic>> cloudQuery = await _firestore
            .collection(_muralsCollection)
            .where('imageHash', isEqualTo: imageHash)
            .limit(1)
            .get();

        if (cloudQuery.docs.isNotEmpty) {
          final Map<String, dynamic> cloudData = cloudQuery.docs.first.data();

          // Update local metadata with cloud status
          await _xmpService.updateMetadata(
            imageHash: imageHash,
            approvalStatus: cloudData['status'] ?? 'pending',
          );
        }
      }

      debugPrint('Metadata sync completed');
    } catch (e) {
      debugPrint('Error syncing metadata: $e');
    }
  }

  /// Get sync status for all local photos
  Future<Map<String, String>> getSyncStatus() async {
    final Map<String, String> syncStatus = <String, String>{};

    try {
      final List<Map<String, dynamic>> localPhotos = await _xmpService.getAllImageMetadata();

      for (final Map<String, dynamic> localPhoto in localPhotos) {
        final String imageHash = localPhoto['imageHash'];
        final String approvalStatus = localPhoto['approvalStatus'] ?? 'local';
        syncStatus[imageHash] = approvalStatus;
      }
    } catch (e) {
      debugPrint('Error getting sync status: $e');
    }

    return syncStatus;
  }

  /// Cache remote photos metadata in local Hive box for unified access
  Future<void> cacheRemotePhotosMetadata() async {
    try {
      final User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('No user logged in, skipping remote cache sync');
        return;
      }

      // Get all remote photos for the current user
      final QuerySnapshot<Map<String, dynamic>> snapshot = await _firestore
          .collection(_muralsCollection)
          .where('userId', isEqualTo: user.uid)
          .get();

      int cachedCount = 0;
      for (final QueryDocumentSnapshot<Map<String, dynamic>> doc in snapshot.docs) {
        final Map<String, dynamic> data = doc.data();
        final String imageHash = data['imageHash'] ?? doc.id;

        // Create MuralMetadata from Firestore data
        final MuralMetadata metadata = MuralMetadata(
          latitude: data['latitude']?.toDouble(),
          longitude: data['longitude']?.toDouble(),
          address: data['address'],
          dateTaken: data['dateTaken'] != null
              ? (data['dateTaken'] as Timestamp).toDate()
              : data['timestamp'] != null
              ? (data['timestamp'] as Timestamp).toDate()
              : null,
          cropCorners: data['cropCorners'] != null
              ? (data['cropCorners'] as List<dynamic>)
                    .map(
                      (dynamic corner) => Offset(
                        (corner['x'] as num).toDouble(),
                        (corner['y'] as num).toDouble(),
                      ),
                    )
                    .toList()
              : null,
          approvalStatus: data['status'] ?? data['approvalStatus'] ?? 'pending',
          userId: data['userId'],
          lastModified: DateTime.now(),
        );

        // Cache in Hive box with a special prefix to distinguish from local photos
        final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
        await box.put('remote_$imageHash', metadata.toMap());
        cachedCount++;
      }

      debugPrint('Cached $cachedCount remote photos metadata in Hive box');
    } catch (e) {
      debugPrint('Error caching remote photos metadata: $e');
    }
  }

  /// Clean up local files after successful upload
  Future<void> cleanupAfterUpload(String imageHash) async {
    try {
      await _xmpService.deleteImageAndMetadata(imageHash);
      debugPrint('Local files cleaned up after upload: $imageHash');
    } catch (e) {
      debugPrint('Error cleaning up local files: $e');
    }
  }
}
