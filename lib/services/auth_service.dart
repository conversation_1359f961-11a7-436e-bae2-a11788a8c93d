import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import 'package:google_sign_in/google_sign_in.dart';

class AuthService {
  AuthService() {
    // Configure GoogleSignIn based on platform
    if (kIsWeb) {
      _googleSignIn = GoogleSignIn(
        clientId: '79912496559-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com',
        scopes: <String>['openid', 'email', 'profile'],
      );
    } else if (defaultTargetPlatform == TargetPlatform.macOS) {
      // On macOS, GoogleSignIn requires a desktop clientId registered in Google Console
      _googleSignIn = GoogleSignIn(
        clientId: '79912496559-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com',
        scopes: <String>['openid', 'email', 'profile'],
      );
    } else {
      _googleSignIn = GoogleSignIn(
        scopes: <String>['email', 'profile'],
      );
    }
  }
  final FirebaseAuth _auth = FirebaseAuth.instance;
  late final GoogleSignIn _googleSignIn;

  // Expose last error details to improve UI messages
  String? lastErrorCode;
  String? lastErrorMessage;

  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<UserCredential?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        // Web-specific sign-in flow
        final GoogleAuthProvider googleProvider = GoogleAuthProvider();
        googleProvider.addScope('email');
        googleProvider.addScope('profile');

        // Use popup for web
        final UserCredential userCredential = await _auth.signInWithPopup(googleProvider);
        return userCredential;
      } else {
        // Mobile sign-in flow
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
        if (googleUser == null) {
          return null; // The user canceled the sign-in
        }
        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
        final AuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        return await _auth.signInWithCredential(credential);
      }
    } on FirebaseAuthException catch (e) {
      lastErrorCode = e.code;
      lastErrorMessage = e.message;
      debugPrint('Firebase Auth Error: ${e.code} - ${e.message}');
      switch (e.code) {
        case 'popup-closed-by-user':
          debugPrint('User closed the popup');
          break;
        case 'popup-blocked':
          debugPrint('Popup was blocked by browser');
          break;
        case 'network-request-failed':
          debugPrint('Network error occurred');
          break;
        default:
          debugPrint('Authentication error: ${e.message}');
      }
      return null;
    } catch (e) {
      debugPrint('General Auth Error: $e');
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      if (!kIsWeb) {
        await _googleSignIn.signOut();
      }
      await _auth.signOut();
    } catch (e) {
      debugPrint('Sign out error: $e');
    }
  }
}
