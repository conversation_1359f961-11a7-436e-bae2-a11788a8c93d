import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:mural/models/mural.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

/// Unified service for handling all photo operations
class PhotoService {
  factory PhotoService() => _instance;
  PhotoService._internal();
  static final PhotoService _instance = PhotoService._internal();

  final MuralMetadataService _muralMetadataService = MuralMetadataService();

  /// Get a photo by hash, handling both local and remote sources
  Future<Photo?> getPhoto(String imageHash) async {
    if (kIsWeb) {
      // On web, there are no local photos to retrieve
      return null;
    }
    try {
      // First try to get local metadata
      final MuralMetadata? metadata = await _muralMetadataService.getPhotoMetadata(imageHash);
      if (metadata == null) {
        return null;
      }

      // Check if local file exists
      final String imageCacheDirPath = await _muralMetadataService.getImageCacheDirPath();
      final String localImagePath = p.join(imageCacheDirPath, '$imageHash.jpg');
      final File localFile = File(localImagePath);

      if (await localFile.exists()) {
        // Validate the local file
        final bool isValid = await validateImage(localFile);
        return Photo(
          imageHash: imageHash,
          imagePath: localImagePath,
          metadata: metadata,
          isLocal: true,
          isCorrupted: !isValid,
          lastModified: await localFile.lastModified(),
        );
      }

      // If no local file, this might be a remote photo reference
      // For now, return null - this could be extended to handle remote photos
      return null;
    } catch (e) {
      debugPrint('Error getting photo $imageHash: $e');
      return null;
    }
  }

  /// Get all local photos
  Future<List<Photo>> getAllPhotos() async {
    try {
      final List<Map<String, dynamic>> allMetadata = await _muralMetadataService.getAllImageMetadata();
      final List<Photo> photos = <Photo>[];

      for (final Map<String, dynamic> metadataMap in allMetadata) {
        final String imageHash = metadataMap['imageHash'];
        final Photo? photo = await getPhoto(imageHash);
        if (photo != null) {
          photos.add(photo);
        }
      }

      return photos;
    } catch (e) {
      debugPrint('Error getting all photos: $e');
      return <Photo>[];
    }
  }

  /// Cache a remote image and return the cached file
  Future<File> cacheImage(String url) async {
    if (kIsWeb) {
      // On web, we can't cache files to local storage, so return a File object with the URL
      // This will be handled appropriately by the calling code
      return File(url);
    }
    final Directory directory = await getApplicationDocumentsDirectory();
    final String fileName = Uri.parse(url).pathSegments.last;
    final String filePath = p.join(directory.path, 'image_cache', fileName);
    final File file = File(filePath);

    // Check if cached file exists and is valid
    if (await file.exists()) {
      if (await validateImage(file)) {
        return file;
      } else {
        // Cached file is corrupted, delete it and re-download
        debugPrint('Cached image is corrupted, re-downloading: $filePath');
        await file.delete();
      }
    }

    // Download and validate the image
    try {
      final http.Response response = await http.get(Uri.parse(url));

      if (response.statusCode != 200) {
        throw Exception('Failed to download image: HTTP ${response.statusCode}');
      }

      // Validate the downloaded image data
      if (!_validateImageBytes(response.bodyBytes)) {
        throw Exception('Downloaded image data is invalid or corrupted');
      }

      // Create directory and save file
      await file.create(recursive: true);
      await file.writeAsBytes(response.bodyBytes);

      // Final validation of saved file
      if (!await validateImage(file)) {
        await file.delete();
        throw Exception('Saved image file failed validation');
      }

      debugPrint('Image downloaded and cached successfully: $filePath');
      return file;
    } catch (e) {
      debugPrint('Error downloading/caching image: $e');
      rethrow;
    }
  }

  /// Validate that an image file is not corrupted
  Future<bool> validateImage(File imageFile) async {
    try {
      if (!await imageFile.exists()) {
        return false;
      }

      final int fileSize = await imageFile.length();
      if (fileSize == 0) {
        debugPrint('Image file is empty: ${imageFile.path}');
        return false;
      }

      // Try to decode the image to verify it's valid
      final Uint8List imageBytes = await imageFile.readAsBytes();
      return _validateImageBytes(imageBytes);
    } catch (e) {
      debugPrint('Error validating image file: $e');
      return false;
    }
  }

  /// Validate image bytes
  bool _validateImageBytes(Uint8List imageBytes) {
    try {
      if (imageBytes.isEmpty) {
        return false;
      }

      // Try to decode the image
      final img.Image? image = img.decodeImage(imageBytes);
      return image != null && image.width > 0 && image.height > 0;
    } catch (e) {
      debugPrint('Error validating image bytes: $e');
      return false;
    }
  }

  /// Clean up corrupted images and return count of cleaned files
  Future<int> cleanupCorruptedImages() async {
    if (kIsWeb) {
      // On web, there are no local images to clean up
      return 0;
    }
    try {
      final String imageCacheDirPath = await _muralMetadataService.getImageCacheDirPath();
      final Directory imageCacheDir = Directory(imageCacheDirPath);

      if (!await imageCacheDir.exists()) {
        return 0;
      }

      final List<FileSystemEntity> allFiles = await imageCacheDir.list().toList();
      final List<File> imageFiles = allFiles.whereType<File>().where((File f) => f.path.endsWith('.jpg')).toList();

      int cleanedCount = 0;
      for (final File imageFile in imageFiles) {
        if (!await validateImage(imageFile)) {
          try {
            await imageFile.delete();
            cleanedCount++;
            debugPrint('Deleted corrupted image: ${imageFile.path}');
          } catch (e) {
            debugPrint('Error deleting corrupted image ${imageFile.path}: $e');
          }
        }
      }

      return cleanedCount;
    } catch (e) {
      debugPrint('Error during cleanup: $e');
      return 0;
    }
  }

  /// Clean up cached remote images and return count of cleaned files
  Future<int> cleanupCorruptedCache() async {
    if (kIsWeb) {
      // On web, there's no local cache to clean up
      return 0;
    }
    try {
      final Directory directory = await getApplicationDocumentsDirectory();
      final Directory cacheDir = Directory(p.join(directory.path, 'image_cache'));

      if (!await cacheDir.exists()) {
        return 0;
      }

      final List<FileSystemEntity> allFiles = await cacheDir.list().toList();
      final List<File> imageFiles = allFiles.whereType<File>().toList();

      int cleanedCount = 0;
      for (final File imageFile in imageFiles) {
        if (!await validateImage(imageFile)) {
          try {
            await imageFile.delete();
            cleanedCount++;
            debugPrint('Deleted corrupted cached image: ${imageFile.path}');
          } catch (e) {
            debugPrint('Error deleting corrupted cached image ${imageFile.path}: $e');
          }
        }
      }

      return cleanedCount;
    } catch (e) {
      debugPrint('Error during cache cleanup: $e');
      return 0;
    }
  }

  /// Get file from cache or download it for remote images
  Future<File> getFile(String url) async {
    return cacheImage(url);
  }

  /// Create a Photo object from legacy data map (for migration)
  Photo createPhotoFromData(Map<String, dynamic> data) {
    final String imageHash = data['imageHash'] ?? '';
    final String? imageUrl = data['imageUrl'];
    final String? imagePath = data['imagePath'];
    final bool isLocal = data['status'] == 'local' || imagePath != null;

    final MuralMetadata metadata = MuralMetadata(
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      address: data['address'],
      dateTaken: data['timestamp'] != null
          ? (data['timestamp'] is DateTime ? data['timestamp'] as DateTime : (data['timestamp'] as dynamic).toDate())
          : data['dateTaken'],
      approvalStatus: data['status'] ?? data['approvalStatus'],

      cropCorners: data['cropCorners'] != null
          ? (data['cropCorners'] as List<dynamic>)
                .map(
                  (dynamic corner) => Offset(
                    (corner['x'] as num).toDouble(),
                    (corner['y'] as num).toDouble(),
                  ),
                )
                .toList()
          : null,
      userId: data['userId'],
      lastModified: DateTime.now(),
    );

    if (isLocal) {
      return Photo(
        imageHash: imageHash,
        imagePath: imagePath ?? imageUrl,
        metadata: metadata,
        isLocal: true,
      );
    } else {
      return Photo.fromRemote(imageHash, imageUrl ?? '', metadata);
    }
  }
}
