import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:exif/exif.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:image/image.dart' as img;
import 'package:mural/models/mural.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

class MuralMetadataService {
  static const String _imageCacheDirName = 'image_cache';

  Future<String> getImageCacheDirPath() async {
    if (kIsWeb) {
      return '/web_cache/$_imageCacheDirName';
    }
    final Directory d = await getApplicationDocumentsDirectory();
    return p.join(d.path, _imageCacheDirName);
  }

  Future<String> _hash(File f) async => sha256.convert(await f.readAsBytes()).toString();

  Future<bool> _valid(File f) async {
    try {
      if (!await f.exists() || await f.length() == 0) {
        return false;
      }
      return img.decodeImage(await f.readAsBytes()) != null;
    } catch (_) {
      return false;
    }
  }

  Future<File> _toJpg(File src, String dstPath) async {
    if (!await _valid(src)) {
      throw Exception('Invalid image: ${src.path}');
    }
    final img.Image? im = img.decodeImage(await src.readAsBytes());
    if (im == null) {
      throw Exception('Decode failed: ${src.path}');
    }
    final File dst = File(dstPath);
    await dst.writeAsBytes(img.encodeJpg(im, quality: 85));
    if (!await _valid(dst)) {
      throw Exception('Converted image invalid: $dstPath');
    }
    return dst;
  }

  Future<File> saveImageAndMetadata({
    required File imageFile,
    required double latitude,
    required double longitude,
    required DateTime date,
    String? address,
    List<Offset>? cropCorners,
    String? approvalStatus,
    String? userId,
  }) async {
    final String hash = await _hash(imageFile);
    final String dir = await getImageCacheDirPath();
    await Directory(dir).create(recursive: true);
    final File jpg = await _toJpg(imageFile, p.join(dir, '$hash.jpg'));
    final MuralMetadata meta = MuralMetadata(
      latitude: latitude,
      longitude: longitude,
      address: address,
      dateTaken: date,
      cropCorners: cropCorners,
      approvalStatus: approvalStatus,
      userId: userId,
      lastModified: DateTime.now(),
    );
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    await box.put(hash, meta.toMap());
    return jpg;
  }

  Future<File> saveImageAndMetadataFromObject({required File imageFile, required MuralMetadata metadata}) async {
    final String hash = await _hash(imageFile);
    final String dir = await getImageCacheDirPath();
    await Directory(dir).create(recursive: true);
    final File jpg = await _toJpg(imageFile, p.join(dir, '$hash.jpg'));
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    await box.put(hash, metadata.copyWith(lastModified: DateTime.now()).toMap());
    return jpg;
  }

  Future<bool> updateMetadata({
    required String imageHash,
    double? latitude,
    double? longitude,
    String? address,
    DateTime? dateTaken,
    List<Offset>? cropCorners,
    String? approvalStatus,
    String? userId,
  }) async {
    final MuralMetadata? cur = await getPhotoMetadata(imageHash);
    if (cur == null) {
      return false;
    }
    final MuralMetadata upd = cur.copyWith(
      latitude: latitude,
      longitude: longitude,
      address: address,
      dateTaken: dateTaken,
      cropCorners: cropCorners,
      approvalStatus: approvalStatus,
      userId: userId,
      lastModified: DateTime.now(),
    );
    await Hive.box<dynamic>('photo_metadata').put(imageHash, upd.toMap());
    return true;
  }

  Future<bool> updateApprovalStatus(String imageHash, String approvalStatus) =>
      updateMetadata(imageHash: imageHash, approvalStatus: approvalStatus);

  Future<bool> updateCropCorners(String imageHash, List<Offset> cropCorners) =>
      updateMetadata(imageHash: imageHash, cropCorners: cropCorners);

  Future<Map<String, dynamic>?> getMetadata(String imageHash) async => (await getPhotoMetadata(imageHash))?.toMap();

  Future<MuralMetadata?> getPhotoMetadata(String imageHash) async {
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    final Map<String, dynamic>? data = (box.get(imageHash) as Map<dynamic, dynamic>?)?.map(
      (dynamic k, dynamic v) => MapEntry<String, dynamic>(k as String, v),
    );
    if (data == null) {
      final MuralMetadata? r = await recreateMetadataFromJpgExif(imageHash);
      if (r != null) {
        await box.put(imageHash, r.toMap());
      }
      return r;
    }
    return MuralMetadata.fromMap(Map<String, dynamic>.from(data));
  }

  Future<List<Map<String, dynamic>>> getAllImageMetadata() async {
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    final List<Map<String, dynamic>> list = <Map<String, dynamic>>[];
    if (kIsWeb) {
      for (final dynamic k in box.keys) {
        final String h = k.toString();
        final Map<String, dynamic>? d = (box.get(h) as Map<dynamic, dynamic>?)?.map(
          (dynamic k, dynamic v) => MapEntry<String, dynamic>(k as String, v),
        );
        if (d != null) {
          list.add(<String, dynamic>{'imageHash': h, ...d});
        }
      }
      return list;
    }
    final String dir = await getImageCacheDirPath();
    for (final dynamic k in box.keys) {
      final String h = k.toString();
      if (await File(p.join(dir, '$h.jpg')).exists()) {
        final Map<String, dynamic>? d = (box.get(h) as Map<dynamic, dynamic>?)?.map(
          (dynamic k, dynamic v) => MapEntry<String, dynamic>(k as String, v),
        );
        if (d != null) {
          list.add(<String, dynamic>{'imageHash': h, ...d});
        }
      }
    }
    return list;
  }

  /// Returns all metadata image hashes regardless of whether the JPG exists
  Future<Set<String>> getAllMetadataHashes() async {
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    return box.keys.map((dynamic k) => k.toString()).toSet();
  }

  Future<int> cleanupOrphanedMetadata() async {
    if (kIsWeb) {
      return 0;
    }
    final Box<dynamic> box = Hive.box<dynamic>('photo_metadata');
    final String dir = await getImageCacheDirPath();
    int cleaned = 0;
    final List<String> del = <String>[];
    for (final dynamic k in box.keys) {
      final String h = k.toString();
      if (!await File(p.join(dir, '$h.jpg')).exists()) {
        del.add(h);
      }
    }
    for (final String h in del) {
      await box.delete(h);
      cleaned++;
    }
    return cleaned;
  }

  Future<void> deleteImageAndMetadata(String imageHash) async {
    final String dir = await getImageCacheDirPath();
    final File f = File(p.join(dir, '$imageHash.jpg'));
    if (await f.exists()) {
      try {
        await f.delete();
      } catch (_) {}
    }
    await Hive.box<dynamic>('photo_metadata').delete(imageHash);
  }

  Future<int> cleanupCorruptedImages() async {
    if (kIsWeb) {
      return 0;
    }
    final String dir = await getImageCacheDirPath();
    final Directory d = Directory(dir);
    if (!await d.exists()) {
      return 0;
    }
    int n = 0;
    for (final File f in (await d.list().toList()).whereType<File>()) {
      if (f.path.endsWith('.jpg') && !await _valid(f)) {
        await f.delete();
        n++;
      }
    }
    return n;
  }

  Future<MuralMetadata?> recreateMetadataFromJpgExif(String imageHash) async {
    if (kIsWeb) {
      return null;
    }
    final String dir = await getImageCacheDirPath();
    final File f = File(p.join(dir, '$imageHash.jpg'));
    if (!await f.exists()) {
      return null;
    }
    try {
      final Map<String, IfdTag> exifTags = await readExifFromBytes(await f.readAsBytes());
      double? lat;
      double? lon;
      DateTime? date;
      if (exifTags.containsKey('GPS GPSLatitude') && exifTags.containsKey('GPS GPSLongitude')) {
        lat = _deg(exifTags['GPS GPSLatitude']!.values);
        lon = _deg(exifTags['GPS GPSLongitude']!.values);
        final String latRef = exifTags['GPS GPSLatitudeRef']?.printable ?? 'N';
        final String lonRef = exifTags['GPS GPSLongitudeRef']?.printable ?? 'E';
        if (latRef == 'S') {
          lat = -lat;
        }
        if (lonRef == 'W') {
          lon = -lon;
        }
      }
      if (exifTags.containsKey('EXIF DateTimeOriginal')) {
        final String? s = exifTags['EXIF DateTimeOriginal']?.printable;
        if (s != null) {
          date = DateTime.tryParse(s.replaceFirst(':', '-').replaceFirst(':', '-').replaceFirst(' ', 'T'));
        }
      }
      if (lat != null || lon != null || date != null) {
        return MuralMetadata(
          latitude: lat,
          longitude: lon,
          dateTaken: date,
          approvalStatus: 'local',
          lastModified: DateTime.now(),
        );
      }
      return null;
    } catch (_) {
      return null;
    }
  }

  double _deg(IfdValues v) {
    final List<dynamic> a = List<dynamic>.from(v.toList());
    final double d = (a[0] is Ratio) ? (a[0] as Ratio).toDouble() : a[0].toDouble();
    final double m = (a[1] is Ratio) ? (a[1] as Ratio).toDouble() : a[1].toDouble();
    final double s = (a[2] is Ratio) ? (a[2] as Ratio).toDouble() : a[2].toDouble();
    return d + (m / 60.0) + (s / 3600.0);
  }
}
