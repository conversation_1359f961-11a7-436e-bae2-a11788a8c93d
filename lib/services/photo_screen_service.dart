import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:mural/models/mural.dart';
import 'package:mural/services/edge_detection_service.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/widgets/my_message.dart';
import 'package:path/path.dart' as p;

class PhotoScreenService {
  final MuralMetadataService _muralMetadataMetadataService = MuralMetadataService();
  final EdgeDetectionService _edgeDetectionService = EdgeDetectionService();

  Future<void> deletePhoto(String imageUrl, String? docId, bool isLocal) async {
    bool localDeleted = true;
    bool storageDeleted = true;
    bool firestoreDeleted = true;

    if (isLocal) {
      try {
        final String imageHash = p.basenameWithoutExtension(imageUrl);
        await _muralMetadataMetadataService.deleteImageAndMetadata(imageHash);
      } catch (e) {
        localDeleted = false;
      }
    } else {
      if (imageUrl.isNotEmpty) {
        try {
          final Reference storageRef = FirebaseStorage.instance.refFromURL(imageUrl);
          await storageRef.delete();
        } catch (e) {
          if (e is FirebaseException && e.code != 'object-not-found') {
            storageDeleted = false;
          }
        }
      }

      if (docId?.isNotEmpty == true) {
        try {
          await FirebaseFirestore.instance.collection('murals').doc(docId!).delete();
        } catch (e) {
          firestoreDeleted = false;
        }
      } else {
        firestoreDeleted = false;
      }
    }

    _showDeleteResult(localDeleted, storageDeleted, firestoreDeleted);
  }

  void _showDeleteResult(bool localDeleted, bool storageDeleted, bool firestoreDeleted) {
    if (!localDeleted && !storageDeleted && !firestoreDeleted) {
      MyMessage.show('Failed to delete photo from all sources.');
    } else if (!localDeleted) {
      MyMessage.show('Photo deleted, but failed to remove local file.');
    } else if (!storageDeleted) {
      MyMessage.show('Photo deleted, but failed to remove from cloud storage.');
    } else if (!firestoreDeleted) {
      MyMessage.show('Photo deleted, but failed to remove database entry.');
    }
  }

  Future<String?> submitPhotoDirectlyToFirebase(
    String userId,
    String imageHash,
    MuralMetadata metadata,
    Uint8List imageBytes,
  ) async {
    try {
      final String fileName = '$imageHash.jpg';
      final Reference storageRef = FirebaseStorage.instance.ref().child('murals/$userId/$fileName');
      await storageRef.putData(imageBytes);
      final String downloadUrl = await storageRef.getDownloadURL();

      // ignore: always_specify_types
      final DocumentReference docRef = await FirebaseFirestore.instance.collection('murals').add(<String, dynamic>{
        'userId': userId,
        'imageUrl': downloadUrl,
        'imageHash': imageHash,
        'latitude': metadata.latitude,
        'longitude': metadata.longitude,
        'address': metadata.address,
        'timestamp': FieldValue.serverTimestamp(),
        'dateTaken': metadata.dateTaken != null
            ? Timestamp.fromDate(metadata.dateTaken!)
            : FieldValue.serverTimestamp(),
        'uploadedAt': FieldValue.serverTimestamp(),
        'cropCorners': metadata.cropCorners
            ?.map((Offset offset) => <String, double>{'x': offset.dx, 'y': offset.dy})
            .toList(),
        'status': 'pending',
        'approvalStatus': 'pending',
      });

      return docRef.id;
    } catch (e) {
      return null;
    }
  }

  Future<Uint8List> getImageBytes(String imagePath) async {
    if (kIsWeb && imagePath.startsWith('data:')) {
      final String base64Data = imagePath.split(',')[1];
      return base64Decode(base64Data);
    } else if (kIsWeb && imagePath.startsWith('http')) {
      final http.Response response = await http.get(Uri.parse(imagePath));
      return response.bodyBytes;
    } else {
      return await File(imagePath).readAsBytes();
    }
  }

  Future<List<Offset>> calculateImageCorners(String imagePath, double containerWidth, double containerHeight) async {
    final Uint8List imageBytes = await getImageBytes(imagePath);
    final img.Image? decodedImage = img.decodeImage(imageBytes);
    if (decodedImage == null) {
      throw Exception('Failed to decode image');
    }
    final double imageWidth = decodedImage.width.toDouble();
    final double imageHeight = decodedImage.height.toDouble();

    final double imageAspectRatio = imageWidth / imageHeight;
    final double containerAspectRatio = containerWidth / containerHeight;

    double displayedImageWidth;
    double displayedImageHeight;
    double offsetX;
    double offsetY;

    if (imageAspectRatio > containerAspectRatio) {
      displayedImageWidth = containerWidth;
      displayedImageHeight = containerWidth / imageAspectRatio;
      offsetX = 0;
      offsetY = (containerHeight - displayedImageHeight) / 2;
    } else {
      displayedImageHeight = containerHeight;
      displayedImageWidth = containerHeight * imageAspectRatio;
      offsetX = (containerWidth - displayedImageWidth) / 2;
      offsetY = 0;
    }

    // Try automatic edge detection first
    try {
      final List<Offset>? detectedCorners = await _edgeDetectionService.detectEdges(imageBytes);
      if (detectedCorners != null && detectedCorners.length == 4) {
        // Convert image coordinates to display coordinates
        final double scaleX = displayedImageWidth / imageWidth;
        final double scaleY = displayedImageHeight / imageHeight;

        return detectedCorners.map((Offset corner) {
          return Offset(
            offsetX + (corner.dx * scaleX),
            offsetY + (corner.dy * scaleY),
          );
        }).toList();
      }
    } catch (e) {
      // Fall back to default corners if edge detection fails
    }

    // Fallback to default corners (full image bounds)
    return <Offset>[
      Offset(offsetX, offsetY),
      Offset(offsetX + displayedImageWidth, offsetY),
      Offset(offsetX + displayedImageWidth, offsetY + displayedImageHeight),
      Offset(offsetX, offsetY + displayedImageHeight),
    ];
  }

  Future<String> createMuralMetadataContent(MuralMetadata metadata) async {
    return '''
<?xml version="1.0" encoding="UTF-8"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/">
  <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
    <rdf:Description rdf:about=""
        xmlns:exif="http://ns.adobe.com/exif/1.0/"
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:mural="http://mural.app/ns/1.0/"
        ${metadata.latitude != null ? 'exif:GPSLatitude="${metadata.latitude}"' : ''}
        ${metadata.longitude != null ? 'exif:GPSLongitude="${metadata.longitude}"' : ''}
        ${metadata.dateTaken != null ? 'exif:DateTimeOriginal="${metadata.dateTaken!.toIso8601String()}"' : ''}
        ${metadata.address != null ? 'dc:coverage="${metadata.address}"' : ''}
        ${metadata.approvalStatus != null ? 'mural:approvalStatus="${metadata.approvalStatus}"' : ''}
        ${metadata.userId != null ? 'mural:userId="${metadata.userId}"' : ''}
        mural:lastModified="${DateTime.now().toIso8601String()}">
    </rdf:Description>
  </rdf:RDF>
</x:xmpmeta>
''';
  }

  void cleanupCorruptedImage(String imagePath) {
    Future<void>.delayed(Duration.zero, () async {
      try {
        final File imageFile = File(imagePath);
        final String fileName = p.basenameWithoutExtension(imagePath);

        if (await imageFile.exists()) {
          await imageFile.delete();
        }

        final String imageCacheDirPath = await _muralMetadataMetadataService.getImageCacheDirPath();
        final String xmpPath = p.join(imageCacheDirPath, '$fileName.xmp');
        final File xmpFile = File(xmpPath);
        if (await xmpFile.exists()) {
          await xmpFile.delete();
        }
      } catch (e) {
        // Silent cleanup failure
      }
    });
  }
}
