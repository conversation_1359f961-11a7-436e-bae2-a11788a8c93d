// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB2fDSw4uA6KoFRYSdmfrErVEKTErBGqZo',
    appId: '1:79912496559:android:15ad8469e1850ed8ca935a',
    messagingSenderId: '79912496559',
    projectId: 'vteam-mural',
    storageBucket: 'vteam-mural.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBduJUL9dgcymglVpaGzOybTv2gcbomflo',
    appId: '1:79912496559:ios:eefe79997df543ecca935a',
    messagingSenderId: '79912496559',
    projectId: 'vteam-mural',
    storageBucket: 'vteam-mural.firebasestorage.app',
    iosClientId: '79912496559-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com',
    iosBundleId: 'com.vteam.mural',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBduJUL9dgcymglVpaGzOybTv2gcbomflo',
    appId: '1:79912496559:ios:eefe79997df543ecca935a',
    messagingSenderId: '79912496559',
    projectId: 'vteam-mural',
    storageBucket: 'vteam-mural.firebasestorage.app',
    iosClientId: '79912496559-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com',
    iosBundleId: 'com.vteam.mural',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB6S2VXUii_bqpOlipmI1Rg5H8BUdCaDKw',
    appId: '1:79912496559:web:d262fcd8c49aa925ca935a',
    messagingSenderId: '79912496559',
    projectId: 'vteam-mural',
    authDomain: 'vteam-mural.firebaseapp.com',
    storageBucket: 'vteam-mural.firebasestorage.app',
    measurementId: 'G-T5GK30711H',
  );
}
