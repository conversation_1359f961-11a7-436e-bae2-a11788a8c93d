// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:math' show atan2;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mural/constants/my_colors.dart';
import 'package:mural/navigator_key.dart';
import 'package:mural/screens/camera_screen.dart';
import 'package:mural/screens/collection_screen.dart';
import 'package:mural/screens/map_screen.dart';
import 'package:mural/screens/review_screen.dart';
import 'package:mural/screens/settings_screen.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/services/photo_service.dart';
import 'package:mural/widgets/adaptive_landsacpe.dart';
import 'package:mural/widgets/buttons_selection.dart';
import 'package:mural/widgets/gradient_screen.dart';
import 'package:mural/widgets/my_message.dart';
import 'package:sensors_plus/sensors_plus.dart';

import 'firebase_options.dart';

enum AppScreen {
  map(0, Icons.map, 'Map'),
  camera(1, Icons.camera_enhance, 'Photo'),
  collection(2, Icons.collections, 'Collection'),
  review(3, Icons.reviews, 'Review'),
  settings(4, Icons.settings, 'Settings');

  const AppScreen(this.screenIndex, this.icon, this.label);
  final int screenIndex;
  final IconData icon;
  final String label;
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  // Init Hive for local metadata storage (web + mobile)
  await Hive.initFlutter();
  if (!Hive.isBoxOpen('photo_metadata')) {
    await Hive.openBox('photo_metadata');
  }
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, this.tileProvider});
  final TileProvider? tileProvider;

  @override
  Widget build(final BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: 'Mural',
      theme: ThemeData(
        primarySwatch: MyColors.themeSwatch,
        textTheme: const TextTheme().apply(bodyColor: Colors.white, displayColor: Colors.white),
        iconTheme: const IconThemeData(color: Colors.white),
        primaryColor: MyColors.themeSwatch,
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            backgroundColor: MyColors.primaryButtonColor,
            foregroundColor: MyColors.buttonTextColor,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: MyColors.primaryButtonColor,
            foregroundColor: MyColors.buttonTextColor,
          ),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: MyColors.gradientStart,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
      ),
      home: HomePage(tileProvider: tileProvider),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key, this.tileProvider});
  final TileProvider? tileProvider;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  AppScreen _selectedScreen = AppScreen.map;
  // ignore: always_specify_types
  StreamSubscription? _subscription;
  // ignore: unused_field
  bool _isLandscape = false;
  double _rotationAngle = 0;
  bool _isReviewer = false;

  Widget _getCurrentWidget() {
    switch (_selectedScreen) {
      case AppScreen.map:
        return MapScreen(tileProvider: widget.tileProvider, rotationAngle: _rotationAngle);
      case AppScreen.camera:
        return CameraScreen(
          rotationAngle: _rotationAngle,
          isLandscape: _isLandscape,
          isSelected: _selectedScreen == AppScreen.camera,
          onPhotoSubmitted: () {
            setState(() {
              _selectedScreen = AppScreen.collection;
            });
          },
        );
      case AppScreen.collection:
        return CollectionScreen(
          initialSelectedStatus: 'local',
          onSwitchToCamera: () {
            setState(() {
              _selectedScreen = AppScreen.camera;
            });
          },
        );
      case AppScreen.review:
        return _isReviewer ? const ReviewScreen() : const SettingsScreen();
      case AppScreen.settings:
        return const SettingsScreen();
    }
  }

  @override
  void initState() {
    super.initState();

    // Clean up any corrupted images on app startup
    _performStartupCleanup();

    if (!kIsWeb && defaultTargetPlatform != TargetPlatform.macOS) {
      _subscription = accelerometerEventStream().listen((AccelerometerEvent event) {
        final double x = event.x;
        final double y = event.y;
        _isLandscape = x.abs() > y.abs();
        final double rotationAngle = atan2(x, y);

        setState(() {
          _rotationAngle = rotationAngle;
        });
      });
    }
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      if (user == null) {
        setState(() {
          _isReviewer = false;
          // Reset to map if user was on review screen and lost access
          if (_selectedScreen == AppScreen.review) {
            _selectedScreen = AppScreen.map;
          }
        });
      } else {
        final DocumentSnapshot<Map<String, dynamic>> userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();
        final bool isReviewer = userDoc.exists && userDoc.data()?['isReviewer'] == true;
        setState(() {
          final bool wasReviewer = _isReviewer;
          _isReviewer = isReviewer;
          // If user lost reviewer status and was on review tab, switch to settings
          if (wasReviewer && !isReviewer && _selectedScreen == AppScreen.review) {
            _selectedScreen = AppScreen.settings;
          }
        });
      }
    });
  }

  /// Performs cleanup of corrupted images on app startup
  Future<void> _performStartupCleanup() async {
    try {
      final PhotoService photoService = PhotoService();
      final MuralMetadataService muralMetadataService = MuralMetadataService();

      // Clean up corrupted images from local storage
      final int corruptedCount = await photoService.cleanupCorruptedImages();

      // Clean up corrupted images from cache
      final int cachedCorruptedCount = await photoService.cleanupCorruptedCache();

      // Clean up metadata
      final int orphanedCount = await muralMetadataService.cleanupOrphanedMetadata();

      final int totalCleaned = corruptedCount + cachedCorruptedCount + orphanedCount;
      if (totalCleaned > 0) {
        final String message =
            'Cleanup completed:\n'
            '• Removed $corruptedCount corrupted images\n'
            '• Removed $cachedCorruptedCount corrupted cached images\n'
            '• Removed $orphanedCount orphaned Mural Metadata entries\n'
            'Total files cleaned: $totalCleaned';

        MyMessage.show(message);
      }
    } catch (e) {
      MyMessage.showError('Error during cleanup: $e');
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void _onItemTapped(final int index) {
    final List<AppScreen> availableScreens = _getAvailableScreens();
    if (index < availableScreens.length) {
      setState(() {
        _selectedScreen = availableScreens[index];
      });
    }
  }

  List<AppScreen> _getAvailableScreens() {
    final List<AppScreen> screens = <AppScreen>[AppScreen.map, AppScreen.camera, AppScreen.collection];
    if (_isReviewer) {
      screens.add(AppScreen.review);
    }
    screens.add(AppScreen.settings);
    return screens;
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.themeSwatch,
      body: Stack(
        children: <Widget>[
          Positioned.fill(
            child: GradientScreen(
              child: Center(
                child: _getCurrentWidget(),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomNavBar(),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    final List<AppScreen> availableScreens = _getAvailableScreens();
    final List<Widget> segments = availableScreens
        .map(
          (AppScreen screen) => adaptiveLandscape(
            isLandscape: _isLandscape,
            angle: _rotationAngle,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Icon(screen.icon, color: Colors.white),
                Text(screen.label),
              ],
            ),
          ),
        )
        .toList();

    return Padding(
      padding: const EdgeInsets.only(bottom: 30),
      child: ButtonsSelection(
        items: segments,
        selectedIndex: availableScreens.indexOf(_selectedScreen),
        onItemSelected: _onItemTapped,
      ),
    );
  }
}
