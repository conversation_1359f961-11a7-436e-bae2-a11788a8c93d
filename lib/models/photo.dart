import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/services/photo_service.dart';

class Photo {
  const Photo({
    required this.imageHash,
    this.imagePath,
    this.imageUrl,
    required this.metadata,
    this.isLocal = true,
    this.isCorrupted = false,
    this.lastModified,
  });

  // Factory constructor to create a Photo object from local paths
  factory Photo.fromPaths(String imagePath, String xmpPath, MuralMetadata metadata) {
    final String imageHash = imagePath.split('/').last.split('.').first;
    return Photo(
      imageHash: imageHash,
      imagePath: imagePath,
      metadata: metadata,
      isLocal: true,
    );
  }

  // Factory constructor for remote photos
  factory Photo.fromRemote(String imageHash, String imageUrl, MuralMetadata metadata) {
    return Photo(
      imageHash: imageHash,
      imageUrl: imageUrl,
      metadata: metadata,
      isLocal: false,
    );
  }

  final String imageHash;
  final String? imagePath; // For local images
  final String? imageUrl; // For remote images
  final MuralMetadata metadata;
  final bool isLocal;
  final bool isCorrupted;
  final DateTime? lastModified;

  // Computed properties
  String get fileName => imagePath?.split('/').last ?? '$imageHash.jpg';

  String get displayTitle => metadata.address ?? 'Unknown Location';

  String get statusText => metadata.approvalStatus ?? 'local';

  String get effectiveImageSource => isLocal ? (imagePath ?? '') : (imageUrl ?? '');

  bool get hasValidSource => isLocal ? (imagePath?.isNotEmpty ?? false) : (imageUrl?.isNotEmpty ?? false);

  Future<String?> getImageUrl() async {
    // On web, we always use the network URL directly.
    if (kIsWeb) {
      return imageUrl;
    }

    // On mobile, we prioritize the local file path if available.
    try {
      if (isLocal) {
        if (imagePath != null) {
          final File file = File(imagePath!);
          if (await file.exists()) {
            return file.path;
          }
        } else {
          final MuralMetadataService xmpService = MuralMetadataService();
          final String imageCacheDirPath = await xmpService.getImageCacheDirPath();
          final String path = '$imageCacheDirPath/$imageHash.jpg';
          final File file = File(path);
          if (await file.exists()) {
            return file.path;
          }
        }
      } else if (imageUrl != null) {
        final PhotoService photoService = PhotoService();
        final File cachedFile = await photoService.cacheImage(imageUrl!);
        return cachedFile.path;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting image file for $imageHash: $e');
      return null;
    }
  }

  // Create a copy with updated properties
  Photo copyWith({
    String? imageHash,
    String? imagePath,
    String? imageUrl,
    MuralMetadata? metadata,
    bool? isLocal,
    bool? isCorrupted,
    DateTime? lastModified,
  }) {
    return Photo(
      imageHash: imageHash ?? this.imageHash,
      imagePath: imagePath ?? this.imagePath,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      isLocal: isLocal ?? this.isLocal,
      isCorrupted: isCorrupted ?? this.isCorrupted,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  @override
  String toString() {
    return 'Photo(hash: $imageHash, isLocal: $isLocal, status: $statusText, corrupted: $isCorrupted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is Photo && other.imageHash == imageHash;
  }

  @override
  int get hashCode => imageHash.hashCode;
}
