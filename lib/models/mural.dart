import 'dart:ui';

/// Data class for comprehensive photo metadata
class MuralMetadata {
  const MuralMetadata({
    this.latitude,
    this.longitude,
    this.address,
    this.dateTaken,
    this.cropCorners,
    this.approvalStatus,
    this.userId,
    this.lastModified,
  });

  factory MuralMetadata.fromMap(Map<String, dynamic> map) {
    List<Offset>? corners;
    if (map['cropCorners'] != null) {
      corners = (map['cropCorners'] as List<dynamic>)
          .map(
            (dynamic corner) =>
                Offset((corner as Map<String, dynamic>)['x']?.toDouble() ?? 0.0, corner['y']?.toDouble() ?? 0.0),
          )
          .toList();
    }

    return MuralMetadata(
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      address: map['address'] as String?,
      dateTaken: map['dateTaken'] != null ? DateTime.tryParse(map['dateTaken'] as String) : null,
      cropCorners: corners,
      approvalStatus: map['approvalStatus'] as String?,
      userId: map['userId'] as String?,
      lastModified: map['lastModified'] != null ? DateTime.tryParse(map['lastModified'] as String) : null,
    );
  }
  final double? latitude;
  final double? longitude;
  final String? address;
  final DateTime? dateTaken;
  final List<Offset>? cropCorners;
  final String? approvalStatus;
  final String? userId;
  final DateTime? lastModified;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'dateTaken': dateTaken?.toIso8601String(),
      'cropCorners': cropCorners?.map((Offset offset) => <String, double>{'x': offset.dx, 'y': offset.dy}).toList(),
      'approvalStatus': approvalStatus,
      'userId': userId,
      'lastModified': lastModified?.toIso8601String(),
    };
  }

  MuralMetadata copyWith({
    double? latitude,
    double? longitude,
    String? address,
    DateTime? dateTaken,
    List<Offset>? cropCorners,
    String? approvalStatus,
    String? userId,
    DateTime? lastModified,
  }) {
    return MuralMetadata(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      dateTaken: dateTaken ?? this.dateTaken,
      cropCorners: cropCorners ?? this.cropCorners,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      userId: userId ?? this.userId,
      lastModified: lastModified ?? this.lastModified,
    );
  }
}
