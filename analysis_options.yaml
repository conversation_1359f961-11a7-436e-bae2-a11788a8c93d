include: package:flutter_lints/flutter.yaml

# analysis_options.yaml
formatter:
  page_width: 120
  trailing_commas: preserve

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.graphql.dart"
    - "**/*.mocks.dart"
    - "**/generated/**"
    - build/**

  errors:
    todo: ignore
    unnecessary_this: ignore
    avoid_empty_else: error
    avoid_function_literals_in_foreach_calls: ignore
    always_put_control_body_on_new_line: error
    curly_braces_in_flow_control_structures: error

  # language:
  #   strict-casts: true
  #   strict-inference: true
  #   strict-raw-types: true

linter:
  rules:
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: false
    always_specify_types: true
    annotate_overrides: true
    # avoid_dynamic_calls: true
    avoid_print: true
    avoid_type_to_string: true
    cancel_subscriptions: true
    close_sinks: true
    directives_ordering: true
    matching_super_parameters: true
    prefer_const_constructors: true
    prefer_expression_function_bodies: false
    prefer_final_fields: true
    prefer_final_locals: true
    prefer_is_empty: true
    prefer_single_quotes: true
    # public_member_api_docs: true
    require_trailing_commas: false
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_pub_dependencies: true
    super_goes_last: false
    use_decorated_box: true
    use_key_in_widget_constructors: true
    use_super_parameters: true
    unnecessary_parenthesis: true
