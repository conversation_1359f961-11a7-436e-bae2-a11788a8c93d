   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Function to check if a user is a reviewer by looking at their document in the 'users' collection.
       function isReviewer(userId) {
         return get(/databases/$(database)/documents/users/$(userId)).data.isReviewer == true;
       }
   
       // Rules for the 'murals' collection
       match /murals/{muralId} {
         allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
         allow read: if true;
   
         // CORRECTED UPDATE RULE:
         // Allow reviewers to update status, and also address/XMP fields
         allow update: if request.auth != null
                       && isReviewer(request.auth.uid)
                       && (request.writeFields.hasOnly(['status']) ||
                           request.writeFields.hasOnly(['address', 'xmpUrl', 'lastUpdated', 'lastModifiedBy']));
   
         allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
       }
   
       // Rules for the 'users' collection
       match /users/{userId} {
         // A user can read their own document.
         allow read: if request.auth != null && request.auth.uid == userId;
         // A user can create their own user document.
         allow create: if request.auth != null && request.auth.uid == userId;
         // Updates should be handled by a backend or manually in the console.
         allow update: if false;
       }
     }
   }