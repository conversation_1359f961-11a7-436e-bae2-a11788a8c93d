# Mural App - Function Specification

## Overview

Mural is a cross-platform mobile and web application for discovering, capturing, and collecting city murals and graffiti. The app uses a local-first architecture with Hive-backed metadata for comprehensive photo metadata management and selective cloud synchronization.

## Core Architecture

### Local-First Design

- Photo Storage: Each photo stored as JPG with metadata in Hive using SHA256 hash-based naming
- **Offline Capability**: Full photo editing and metadata management without internet
- **Selective Sync**: Users choose which photos to submit to Firebase
- Data Portability: Export/import tooling can serialize metadata when needed

### Cross-Platform Support

- **Mobile**: iOS and Android with full file system access
- **Web**: Browser-based with IndexedDB storage and data URL handling
- **Platform Abstraction**: PhotoWidget and services handle platform differences

## Core Functions

### 1. Photo Capture & Management

#### Camera Integration

```dart
Future<void> takePicture()
```

- Captures photos using device camera
- Automatically saves as hash-named JPG files
- Stores metadata (Hive) with GPS coordinates and timestamp
- Optional user image processing (perspective correction)

#### Photo Storage

```dart
Future<File> saveImageAndMetadata(File imageFile, PhotoMetadata metadata)
```

- Converts images to JPG format for consistency
- Generates SHA256 hash for filename
- Writes metadata into Hive with comprehensive fields
- Prevents duplicate storage through hash-based deduplication

#### Metadata Management

```dart
Future<PhotoMetadata?> getPhotoMetadata(String imageHash)
Future<bool> updateMetadata({String imageHash, ...})
```

- Reads/writes metadata from Hive
- Supports GPS coordinates, addresses, timestamps
- Tracks crop corners for distortion correction
- Manages approval status and user information

### 2. Photo Editing & Processing

#### Permission System

```dart
bool _canEditPhoto()
bool _canDeletePhoto()
```

- **Editing Permissions**: Owners can edit until approval; reviewers can edit anytime
- **Deletion Permissions**: Owners can delete pending/rejected photos; reviewers can delete anytime
- **Review Mode**: Special permissions when accessed from ReviewScreen
- **Cross-Platform**: All permissions work on mobile and web clients

#### Perspective Correction

```dart
Future<void> _applyRectangleAdjustment()
```

- Interactive corner adjustment for perspective correction
- Real-time preview with RectangleAdjuster widget
- Bilinear interpolation for image warping
- Cross-platform support (file-based on mobile, data URLs on web)

#### Crop Management

```dart
Future<bool> updateCropCorners(String imageHash, List<Offset> corners)
```

- Stores crop corner coordinates in metadata (Hive)
- Enables Original/Modified view toggle
- Preserves original image with backup system
- Automatic distortion detection from corner presence

### 3. Cloud Synchronization

#### Firebase Integration

```dart
Future<String?> submitPhotoWithMetadata({String imageHash, PhotoMetadata metadata})
```

- Uploads JPG to Firebase Storage and writes searchable metadata to Firestore
- Creates Firestore documents with searchable metadata
- Implements hash-based duplicate detection
- Supports both mobile (BackendSyncService) and web (direct Firebase) submission

#### Sync Status Management

```dart
Future<Map<String, String>> getSyncStatus()
Future<void> syncMetadataUpdates()
```

- Tracks local vs cloud photo status
- Updates local metadata with cloud approval status
- Manages pending/approved/rejected states
- Provides sync statistics and cleanup operations

### 4. User Interface Components

#### Photo Display

```dart
class PhotoWidget extends StatefulWidget
```

- Cross-platform image display abstraction
- Handles local files, remote URLs, and data URLs
- Graceful error handling with corruption detection
- Loading states and metadata overlays

#### Navigation & Screens

- **CameraScreen**: Photo capture with live preview and editing
- **HistoryScreen**: Local/remote photo management with filtering
- **PhotoScreen**: Full-screen viewing with editing capabilities
- **MapScreen**: Geographic visualization of mural locations
- **ReviewScreen**: Admin interface for photo approval

### 5. Data Management

#### Local Storage

```dart
Future<List<Map<String, dynamic>>> getAllImageMetadata()
Future<int> cleanupCorruptedImages()
```

- Scans image cache directory for JPG files and validates associated metadata entries
- Validates image integrity and removes corrupted files
- Manages orphaned metadata cleanup
- Provides storage statistics and file management

#### Cache Management

```dart
Future<File> cacheImage(String url)
Future<int> cleanupCorruptedCache()
```

- Downloads and caches remote images
- Validates cached image integrity
- Implements automatic cache cleanup
- Handles network failures gracefully

### 6. Authentication & Security

#### Google OAuth Integration

```dart
Future<UserCredential?> signInWithGoogle()
```

- Platform-specific authentication (mobile vs web)
- Automatic user session management
- Secure token handling and refresh
- Sign-out with proper cleanup

#### Data Privacy

- Local-first approach minimizes cloud data exposure
- User controls what gets uploaded
- Metadata can be sanitized before sharing
- Secure Firebase rules for data access

### 7. Platform-Specific Features

#### Mobile-Only Features

- File system access for direct image manipulation
- Camera hardware integration
- Accelerometer for orientation detection
- Background processing capabilities

#### Web-Specific Adaptations

- Data URL handling for image processing
- IndexedDB for local storage simulation
- HTTP-based image loading
- Browser security constraint handling

### 8. Error Handling & Recovery

#### Corruption Detection

```dart
Future<bool> validateImage(File imageFile)
bool _validateImageBytes(Uint8List imageBytes)
```

- Automatic image validation using image decoding
- Corruption detection and cleanup
- Graceful degradation for missing files

#### Network Resilience

- Offline-first operation
- Retry mechanisms for failed uploads
- Graceful handling of network timeouts
- User feedback for connection issues

## Technical Specifications

### File Formats

- Images: JPG format (converted from any input format)
- Metadata: Hive-backed storage (local database)
- Naming: SHA256 hash-based filenames for deduplication

### Storage Locations

- **Mobile**: `Documents/image_cache/` directory
- **Web**: Browser IndexedDB with data URL fallbacks
- **Cloud**: Firebase Storage with organized folder structure

### Performance Optimizations

- Lazy loading for large photo collections
- Image compression (85% quality for uploads)
- Efficient pagination for remote data
- Background processing for non-critical operations

### Security Measures

- Hash-based filename generation prevents path traversal
- Firebase security rules enforce user-based access
- No sensitive data in client-side code
- Secure authentication token management

## API Endpoints (Firebase)

### Firestore Collections

- **`/murals`**: Photo metadata with approval workflow
- **User-based security**: Users can only modify their own photos
- **Admin access**: Special permissions for review/approval

### Storage Structure

```bash
/murals/{userId}/{imageHash}.jpg
```

## Future Extensibility

### Plugin Architecture

- Modular metadata services
- Extensible photo processing pipeline
- Third-party integration capabilities
- Custom export formats

### Scalability Considerations

- Efficient database indexing for geographic queries
- CDN integration for global image delivery
- Batch processing for large operations
- Horizontal scaling support

## Development Guidelines

### Code Organization

- Service-based architecture with clear separation
- Platform abstraction through dedicated widgets
- Comprehensive error handling at all levels
- Extensive logging for debugging and monitoring

### Testing Strategy

- Unit tests for core business logic
- Integration tests for Firebase operations
- Platform-specific testing for mobile/web differences
- Mock services for offline development

### Deployment

- Multi-platform build pipeline
- Environment-specific configuration
- Automated testing and quality checks
- Progressive rollout capabilities
