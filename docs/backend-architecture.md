# Backend Architecture: Photo + XMP System

## Overview

The transition from Firebase-only metadata storage to local XMP sidecar files fundamentally changes the backend architecture. This document outlines the recommended approach for a hybrid local-first + cloud sync system.

## Current System Analysis

### Before (Firebase-Only)

- All metadata stored in Firestore
- Photos uploaded immediately to Firebase Storage
- No offline editing capabilities
- Limited metadata richness

### After (Photo + XMP)

- Rich metadata stored locally in XMP files
- Offline photo editing and metadata management
- Selective upload to Firebase for sharing/review
- Hash-based deduplication system

## Recommended Backend Architecture

### 1. Hybrid Local-First + Cloud Sync Model

```diagram
Local Device              Firebase Backend           Admin/Review
┌─────────────────┐      ┌──────────────────┐      ┌─────────────────┐
│ ┌─────────────┐ │      │ ┌──────────────┐ │      │                 │
│ │ photo.jpg   │ │─────▶│ │ Storage      │ │─────▶│ Review Process  │
│ │ photo.xmp   │ │      │ │ /murals/     │ │      │                 │
│ └─────────────┘ │      │ └──────────────┘ │      │                 │
│                 │      │ ┌──────────────┐ │      │                 │
│ Rich Metadata   │─────▶│ │ Firestore    │ │─────▶│ Analytics       │
│ Offline Editing │      │ │ /murals      │ │      │                 │
└─────────────────┘      │ └──────────────┘ │      └─────────────────┘
                         └──────────────────┘
```

### 2. Enhanced Firestore Schema

```javascript
// /murals/{docId}
{
  // Core identification
  userId: string,
  imageUrl: string,         // Firebase Storage URL for JPG image
  xmpUrl: string,           // Firebase Storage URL for XMP sidecar file
  imageHash: string,        // SHA256 hash for deduplication
  
  // Location data
  latitude: number,
  longitude: number,
  address: string,
  
  // Temporal data
  timestamp: Timestamp,     // When photo was taken (from XMP)
  dateTaken: Timestamp,     // Duplicate for clarity
  uploadedAt: Timestamp,    // When uploaded to Firebase
  
  // Processing metadata
  cropCorners: Array<{x: number, y: number}>,
  
  // Workflow status
  status: 'pending' | 'approved' | 'rejected',
  approvalStatus: 'pending' | 'approved' | 'rejected',
  
  // Schema evolution
  xmpVersion: string,       // Track XMP schema version
  metadataSource: 'xmp',    // Track data source
}
```

### 3. Storage Structure

```bash
Firebase Storage:
/murals/
  /{userId}/
    /{imageHash}.jpg      // JPG image file
    /{imageHash}.xmp      // XMP sidecar metadata file
    /thumbnails/
      /{imageHash}_thumb.jpg
```

## Key Backend Strategies

### 1. Deduplication Strategy

**Problem**: Multiple users might photograph the same mural
**Solution**: Hash-based deduplication

```dart
// Enhanced upload with deduplication check
Future<void> submitPhotoWithDeduplication() async {
  // Check if hash already exists
  final QuerySnapshot existing = await FirebaseFirestore.instance
    .collection('murals')
    .where('imageHash', isEqualTo: imageHash)
    .limit(1)
    .get();
    
  if (existing.docs.isNotEmpty) {
    // Handle duplicate - maybe just update metadata
    await handleDuplicatePhoto(existing.docs.first);
  } else {
    // Upload new photo
    await uploadNewPhoto();
  }
}
```

### 2. Sync Strategy Options

#### Option A: Upload-Only (Current)

- Local photos stay local until manually submitted
- Firebase only contains submitted photos
- Simple but limited collaboration

#### Option B: Selective Sync

- Sync metadata only, keep photos local
- Upload photos on-demand for sharing
- Better for bandwidth management

#### Option C: Full Sync with Conflict Resolution

- Sync both photos and metadata
- Handle conflicts when multiple users edit same mural
- Most complex but most collaborative

### 3. Offline-First Considerations

#### Local State Management

```dart
// Track sync status in XMP metadata
class PhotoMetadata {
  // ... existing fields
  String syncStatus;      // 'local', 'syncing', 'synced', 'conflict'
  DateTime? lastSynced;
  String? cloudDocId;     // Reference to Firestore doc
}
```

#### Conflict Resolution

- Last-write-wins for simple conflicts
- Manual resolution for complex conflicts
- Version tracking in XMP metadata

### 4. Performance Optimizations

#### Lazy Loading

- Load thumbnails first, full images on demand
- Progressive metadata loading
- Efficient pagination for large collections

#### Caching Strategy

- Local SQLite cache for Firebase data
- Smart cache invalidation
- Offline-capable queries

## Implementation Phases

### Phase 1: Enhanced Upload (Current)

- ✅ Rich metadata in Firestore
- ✅ Hash-based filenames
- ✅ XMP metadata preservation

### Phase 2: Deduplication

- Hash-based duplicate detection
- Metadata merging strategies
- User notification for duplicates

### Phase 3: Bidirectional Sync

- Download approved photos with metadata
- Sync status tracking
- Conflict resolution UI

### Phase 4: Collaborative Features

- Real-time updates
- Shared editing sessions
- Community contributions

## Security Considerations

### Firebase Rules Enhancement

```javascript
// Enhanced security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /murals/{muralId} {
      allow read: if true; // Public read
      allow create: if request.auth != null 
        && request.auth.uid == resource.data.userId
        && validateMuralData(request.resource.data);
      allow update: if request.auth != null 
        && (request.auth.uid == resource.data.userId 
            || isReviewer(request.auth.uid));
    }
  }
}
```

### Data Validation

- XMP schema validation
- Image format verification
- Metadata sanitization
- Rate limiting for uploads

## Monitoring and Analytics

### Key Metrics

- Local vs uploaded photo ratios
- XMP metadata completeness
- Upload success rates
- Deduplication effectiveness

### Error Handling

- Graceful degradation for network issues
- Retry mechanisms for failed uploads
- User feedback for sync status

## Migration Strategy

### From Current System

1. Maintain backward compatibility
2. Gradual feature rollout
3. Data migration tools
4. User education and onboarding

### Future Extensibility

- Plugin architecture for new metadata types
- API for third-party integrations
- Export capabilities for data portability
