name: mural
description: City Mural Collection Quest
publish_to: "none"

version: 0.0.3+1

environment:
  sdk: ^3.8.1
  flutter: ^3.32.5

dependencies:
  camera: ^0.11.2
  cloud_firestore: ^5.1.0
  crypto: ^3.0.3
  exif: ^3.2.0
  file_selector: ^1.0.3
  firebase_auth: ^5.1.2
  firebase_core: ^3.2.0
  firebase_storage: ^12.1.0
  flutter:
    sdk: flutter
  flutter_doc_scanner: ^0.0.4
  flutter_map: ^8.1.1
  flutter_svg: ^2.0.9
  geocoding: ^4.0.0
  geolocator: ^14.0.2
  google_sign_in: ^6.2.1
  heif_converter: ^1.0.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.1
  image: ^4.2.0
  image_picker: ^1.1.2
  latlong2: ^0.9.0
  package_info_plus: ^8.0.0
  path: ^1.9.0
  path_provider: ^2.1.3
  permission_handler: ^12.0.1
  sensors_plus: ^6.1.1
  shared_preferences: ^2.2.3
  url_launcher: ^6.3.0
  vector_math: ^2.1.4
  xml: ^6.5.0

dev_dependencies:
  firebase_core_platform_interface: any
  flutter_launcher_icons: ^0.14.4
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  path_provider_platform_interface: ^2.1.2
  plugin_platform_interface: ^2.1.8

flutter:
  uses-material-design: true
  assets:
    - assets/app.png
    - assets/google_logo.svg
    - assets/camera_icon.svg
    - assets/spray_can_icon.svg

# dart run flutter_launcher_icons

flutter_launcher_icons:
  android: "launcher_icon"
  min_sdk_android: 21
  ios:
    remove_alpha_ios: true
  image_path: "assets/app.png"
  macos:
    generate: true
    image_path: "assets/app.png"
  web:
    generate: true
    image_path: "assets/app.png"
