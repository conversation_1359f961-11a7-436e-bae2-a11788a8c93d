import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image/image.dart' as img;
import 'package:mural/services/edge_detection_service.dart';

void main() {
  group('Edge Detection Tests', () {
    late EdgeDetectionService edgeDetectionService;
    late Uint8List testImageBytes;
    late img.Image testImage;

    setUpAll(() async {
      edgeDetectionService = EdgeDetectionService();

      // Create test image with clear rectangular shape
      testImage = img.Image(width: 400, height: 300);
      img.fill(testImage, color: img.ColorRgb8(255, 255, 255)); // White background

      // Draw a black rectangle that should be detected
      img.fillRect(testImage, x1: 50, y1: 40, x2: 350, y2: 260, color: img.ColorRgb8(0, 0, 0));

      testImageBytes = Uint8List.fromList(img.encodeJpg(testImage));
    });

    test('detectEdges returns valid corner coordinates', () async {
      final List<Offset>? corners = await edgeDetectionService.detectEdges(testImageBytes);

      expect(corners, isNotNull);
      expect(corners!.length, equals(4));

      // Verify corners are in correct order: top-left, top-right, bottom-right, bottom-left
      expect(corners[0].dx, lessThan(corners[1].dx)); // Top-left X < Top-right X
      expect(corners[0].dy, lessThan(corners[3].dy)); // Top-left Y < Bottom-left Y
      expect(corners[1].dy, lessThan(corners[2].dy)); // Top-right Y < Bottom-right Y
      expect(corners[2].dx, greaterThan(corners[3].dx)); // Bottom-right X > Bottom-left X

      // Verify corners are within image bounds
      for (final Offset corner in corners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThanOrEqualTo(testImage.width));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThanOrEqualTo(testImage.height));
      }
    });

    test('detectEdges handles empty image', () async {
      // Create a completely blank image with no edges
      final img.Image blankImage = img.Image(width: 100, height: 100);
      img.fill(blankImage, color: img.ColorRgb8(128, 128, 128)); // Uniform gray

      final Uint8List blankBytes = Uint8List.fromList(img.encodeJpg(blankImage));
      final List<Offset>? corners = await edgeDetectionService.detectEdges(blankBytes);

      // Should return fallback corners for images with no detectable edges
      expect(corners, isNotNull);
      expect(corners!.length, equals(4));
    });

    test('detectEdges with real test image produces reasonable corners', () async {
      // Test with actual test image if it exists
      final File testImageFile = File('assets/test_image_to_crop.png');
      if (await testImageFile.exists()) {
        final Uint8List realImageBytes = await testImageFile.readAsBytes();
        final List<Offset>? corners = await edgeDetectionService.detectEdges(realImageBytes);

        expect(corners, isNotNull);
        expect(corners!.length, equals(4));

        // For 1090x1544 image, corners should be reasonable
        for (final Offset corner in corners) {
          expect(corner.dx, greaterThanOrEqualTo(0));
          expect(corner.dx, lessThanOrEqualTo(1090));
          expect(corner.dy, greaterThanOrEqualTo(0));
          expect(corner.dy, lessThanOrEqualTo(1560));
        }

        // Create debug image with detected edges drawn in blue
        final img.Image? originalImage = img.decodePng(realImageBytes);
        if (originalImage != null) {
          final img.Image debugImage = img.Image.from(originalImage);

          // Draw blue lines connecting the detected corners
          for (int i = 0; i < 4; i++) {
            final Offset start = corners[i];
            final Offset end = corners[(i + 1) % 4];
            img.drawLine(
              debugImage,
              x1: start.dx.round(),
              y1: start.dy.round(),
              x2: end.dx.round(),
              y2: end.dy.round(),
              color: img.ColorRgb8(0, 0, 255), // Blue
              thickness: 5,
            );
          }

          // Save debug image
          final File debugFile = File('/Users/<USER>/src/github/mural/assets/test_image_detected_edge.jpg');
          await debugFile.writeAsBytes(img.encodeJpg(debugImage, quality: 90));
          debugPrint('Debug image with detected edges saved to: \${debugFile.path}');
          debugPrint('Detected corners: \$corners');
        }
      }
    });

    test('detectCorners returns valid corner points', () async {
      final List<Offset> corners = await edgeDetectionService.detectCorners(testImageBytes);

      expect(corners, isNotNull);
      expect(corners, isA<List<Offset>>());

      // Verify all corners are within image bounds
      for (final Offset corner in corners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThanOrEqualTo(testImage.width));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThanOrEqualTo(testImage.height));
      }
    });

    test('edge detection should detect document boundaries', () async {
      // Create image with document-like rectangular shape
      final img.Image documentImage = img.Image(width: 600, height: 800);
      img.fill(documentImage, color: img.ColorRgb8(200, 200, 200)); // Gray background

      // White document with black border
      img.fillRect(documentImage, x1: 100, y1: 150, x2: 500, y2: 650, color: img.ColorRgb8(255, 255, 255));
      img.drawRect(documentImage, x1: 100, y1: 150, x2: 500, y2: 650, color: img.ColorRgb8(0, 0, 0));

      final Uint8List documentBytes = Uint8List.fromList(img.encodeJpg(documentImage));
      final List<Offset>? corners = await edgeDetectionService.detectEdges(documentBytes);

      expect(corners, isNotNull);
      expect(corners!.length, equals(4));

      // TODO: When implemented, corners should be close to actual document boundaries
      // Expected corners should be near: (100,150), (500,150), (500,650), (100,650)
    });

    test('edge detection should handle skewed documents', () async {
      // Test case for perspective-corrected documents
      final List<Offset>? corners = await edgeDetectionService.detectEdges(testImageBytes);

      expect(corners, isNotNull);
      // TODO: When implemented, should detect skewed rectangles and return appropriate corners
    });

    test('edge detection should filter noise', () async {
      // Create noisy image
      final img.Image noisyImage = img.Image(width: 400, height: 300);
      img.fill(noisyImage, color: img.ColorRgb8(128, 128, 128));

      // Add random noise
      for (int i = 0; i < 1000; i++) {
        final int x = (i * 37) % 400;
        final int y = (i * 23) % 300;
        noisyImage.setPixel(x, y, img.ColorRgb8(255, 255, 255));
      }

      // Add clear rectangle that should be detected despite noise
      img.fillRect(noisyImage, x1: 100, y1: 75, x2: 300, y2: 225, color: img.ColorRgb8(0, 0, 0));

      final Uint8List noisyBytes = Uint8List.fromList(img.encodeJpg(noisyImage));
      final List<Offset>? corners = await edgeDetectionService.detectEdges(noisyBytes);

      expect(corners, isNotNull);
      // TODO: When implemented, should detect the rectangle despite noise
    });

    test('edge detection performance with large images', () async {
      // Test with large image
      final img.Image largeImage = img.Image(width: 2000, height: 1500);
      img.fill(largeImage, color: img.ColorRgb8(255, 255, 255));
      img.fillRect(largeImage, x1: 200, y1: 150, x2: 1800, y2: 1350, color: img.ColorRgb8(0, 0, 0));

      final Uint8List largeBytes = Uint8List.fromList(img.encodeJpg(largeImage));

      final Stopwatch stopwatch = Stopwatch()..start();
      final List<Offset>? corners = await edgeDetectionService.detectEdges(largeBytes);
      stopwatch.stop();

      expect(corners, isNotNull);
      // TODO: When implemented, should complete within reasonable time (< 5 seconds)
      // expect(stopwatch.elapsedMilliseconds, lessThan(5000));
    });
  });
}
