import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/services/mural_metadata_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    // Mock path_provider for tests
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      (MethodCall methodCall) async {
        if (methodCall.method == 'getApplicationDocumentsDirectory') {
          return '/tmp/test_documents';
        }
        return null;
      },
    );

    // Init Hive for tests
    final Directory hiveDir = await Directory.systemTemp.createTemp('mural_hive_test');
    Hive.init(hiveDir.path);
    if (!Hive.isBoxOpen('photo_metadata')) {
      await Hive.openBox('photo_metadata');
    }
  });
  group('Metadata Recreation from EXIF', () {
    late Directory testDir;
    late MuralMetadataService service;

    setUp(() async {
      testDir = await Directory.systemTemp.createTemp('metadata_recreation_test');
      service = MuralMetadataService();
    });

    tearDown(() async {
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
    });

    tearDownAll(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.flutter.io/path_provider'),
        null,
      );
    });

    test('should recreate metadata from JPG with EXIF GPS data', () async {
      // Create test JPG file in the image cache directory
      final String imageCacheDirPath = await service.getImageCacheDirPath();
      final Directory imageCacheDir = Directory(imageCacheDirPath);
      await imageCacheDir.create(recursive: true);

      final File testJpgFile = File('$imageCacheDirPath/test_image.jpg');

      // Create a minimal JPEG header (this won't have real EXIF data)
      final List<int> jpegHeader = <int>[
        0xFF, 0xD8, // SOI marker
        0xFF, 0xE0, // APP0 marker
        0x00, 0x10, // Length
        0x4A, 0x46, 0x49, 0x46, 0x00, // "JFIF\0"
        0x01, 0x01, // Version
        0x01, // Units
        0x00, 0x48, 0x00, 0x48, // X and Y density
        0x00, 0x00, // Thumbnail width and height
        0xFF, 0xD9, // EOI marker
      ];

      await testJpgFile.writeAsBytes(jpegHeader);

      // Test the recreation method
      final MuralMetadata? metadata = await service.recreateMetadataFromJpgExif('test_image');

      // Since our test image doesn't have real EXIF data, this should return null
      expect(metadata, isNull);

      // Clean up
      await testJpgFile.delete();
    });

    test('should handle missing JPG file gracefully', () async {
      // Test with non-existent file
      final MuralMetadata? metadata = await service.recreateMetadataFromJpgExif('nonexistent');

      expect(metadata, isNull);
    });

    test('should handle JPG file without EXIF data', () async {
      // Create test JPG file in the image cache directory
      final String imageCacheDirPath = await service.getImageCacheDirPath();
      final Directory imageCacheDir = Directory(imageCacheDirPath);
      await imageCacheDir.create(recursive: true);

      final File testJpgFile = File('$imageCacheDirPath/no_exif.jpg');
      final List<int> minimalJpeg = <int>[0xFF, 0xD8, 0xFF, 0xD9]; // Minimal JPEG
      await testJpgFile.writeAsBytes(minimalJpeg);

      final MuralMetadata? metadata = await service.recreateMetadataFromJpgExif('no_exif');

      expect(metadata, isNull);

      // Clean up
      await testJpgFile.delete();
    });
  });
}
