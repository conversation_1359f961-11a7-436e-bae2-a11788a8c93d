import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockPathProviderPlatform extends Fake with MockPlatformInterfaceMixin implements PathProviderPlatform {
  @override
  Future<String?> getApplicationDocumentsPath() async {
    return '/tmp/test_documents';
  }
}

void main() {
  group('MuralMetadataService', () {
    late MuralMetadataService service;
    late Directory testDir;
    late Directory hiveDir;

    setUpAll(() async {
      PathProviderPlatform.instance = MockPathProviderPlatform();
      service = MuralMetadataService();
      testDir = Directory('/tmp/test_documents/image_cache');
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
      await testDir.create(recursive: true);

      // Init Hive for tests
      hiveDir = await Directory.systemTemp.createTemp('mural_hive_test');
      Hive.init(hiveDir.path);
      if (!Hive.isBoxOpen('photo_metadata')) {
        await Hive.openBox('photo_metadata');
      }
    });

    tearDownAll(() async {
      if (Hive.isBoxOpen('photo_metadata')) {
        await Hive.box('photo_metadata').close();
      }
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
      if (await hiveDir.exists()) {
        await hiveDir.delete(recursive: true);
      }
    });

    test('should save and retrieve comprehensive metadata', () async {
      // Create a test image file
      final File testImageFile = File('${testDir.path}/test_image.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      final MuralMetadata testMetadata = MuralMetadata(
        latitude: 37.7749,
        longitude: -122.4194,
        address: 'San Francisco, CA',
        dateTaken: DateTime(2024, 1, 15, 10, 30),
        cropCorners: <Offset>[
          const Offset(10, 10),
          const Offset(100, 10),
          const Offset(100, 100),
          const Offset(10, 100),
        ],
        approvalStatus: 'pending',
        userId: 'test_user_123',
      );

      // Save image with metadata
      final File savedFile = await service.saveImageAndMetadataFromObject(
        imageFile: testImageFile,
        metadata: testMetadata,
      );

      expect(await savedFile.exists(), isTrue);

      // Calculate hash to retrieve metadata
      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Retrieve metadata
      final MuralMetadata? retrievedMetadata = await service.getPhotoMetadata(imageHash);

      expect(retrievedMetadata, isNotNull);
      expect(retrievedMetadata!.latitude, equals(37.7749));
      expect(retrievedMetadata.longitude, equals(-122.4194));
      expect(retrievedMetadata.address, equals('San Francisco, CA'));
      expect(retrievedMetadata.dateTaken, equals(DateTime(2024, 1, 15, 10, 30)));
      expect(retrievedMetadata.cropCorners?.isNotEmpty, isTrue);
      expect(retrievedMetadata.approvalStatus, equals('pending'));
      expect(retrievedMetadata.userId, equals('test_user_123'));
      expect(retrievedMetadata.cropCorners, hasLength(4));
      expect(retrievedMetadata.cropCorners![0], equals(const Offset(10, 10)));
    });

    test('should update approval status', () async {
      // Create a test image file
      final File testImageFile = File('${testDir.path}/test_image2.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      // Save initial metadata
      final File savedFile = await service.saveImageAndMetadata(
        imageFile: testImageFile,
        latitude: 40.7128,
        longitude: -74.0060,
        date: DateTime.now(),
        approvalStatus: 'pending',
      );

      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Update approval status
      final bool updateResult = await service.updateApprovalStatus(imageHash, 'approved');
      expect(updateResult, isTrue);

      // Verify update
      final MuralMetadata? updatedMetadata = await service.getPhotoMetadata(imageHash);
      expect(updatedMetadata!.approvalStatus, equals('approved'));
    });

    test('should update crop corners', () async {
      // Create a test image file
      final File testImageFile = File('${testDir.path}/test_image3.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      // Save initial metadata
      final File savedFile = await service.saveImageAndMetadata(
        imageFile: testImageFile,
        latitude: 51.5074,
        longitude: -0.1278,
        date: DateTime.now(),
      );

      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Update crop corners
      final List<Offset> newCorners = <Offset>[
        const Offset(20, 20),
        const Offset(200, 20),
        const Offset(200, 200),
        const Offset(20, 200),
      ];

      final bool updateResult = await service.updateCropCorners(
        imageHash,
        newCorners,
      );
      expect(updateResult, isTrue);

      // Verify update
      final MuralMetadata? updatedMetadata = await service.getPhotoMetadata(imageHash);
      expect(updatedMetadata!.cropCorners, hasLength(4));
      expect(updatedMetadata.cropCorners![0], equals(const Offset(20, 20)));
      expect(updatedMetadata.cropCorners?.isNotEmpty, isTrue);
    });

    test('should handle missing metadata gracefully', () async {
      final MuralMetadata? nonExistentMetadata = await service.getPhotoMetadata('nonexistent_hash');
      expect(nonExistentMetadata, isNull);

      final bool updateResult = await service.updateApprovalStatus('nonexistent_hash', 'approved');
      expect(updateResult, isFalse);
    });

    test('should handle delete operation when files are missing', () async {
      // Try to delete files that don't exist - should not throw an error
      await expectLater(service.deleteImageAndMetadata('nonexistent_hash'), completes);

      // Create a file, delete it manually, then try to delete via service
      final File testImageFile = File('${testDir.path}/test_image_delete.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      final File savedFile = await service.saveImageAndMetadata(
        imageFile: testImageFile,
        latitude: 40.7128,
        longitude: -74.0060,
        date: DateTime.now(),
      );

      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Manually delete the image file to simulate missing file scenario
      if (await savedFile.exists()) {
        await savedFile.delete();
      }

      // Service delete should still work gracefully
      await expectLater(service.deleteImageAndMetadata(imageHash), completes);
    });

    test('should only return metadata for images that exist', () async {
      // Create a test image and save it
      final File testImageFile = File('${testDir.path}/test_existing.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      final File savedFile = await service.saveImageAndMetadata(
        imageFile: testImageFile,
        latitude: 40.7128,
        longitude: -74.0060,
        date: DateTime.now(),
      );

      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Verify it appears in getAllImageMetadata
      List<Map<String, dynamic>> allMetadata = await service.getAllImageMetadata();
      expect(allMetadata.any((Map<String, dynamic> m) => m['imageHash'] == imageHash), isTrue);

      // Delete the image file
      await savedFile.delete();

      // Verify it no longer appears in getAllImageMetadata (Hive entry remains, but filtered out)
      allMetadata = await service.getAllImageMetadata();
      expect(allMetadata.any((Map<String, dynamic> m) => m['imageHash'] == imageHash), isFalse);
    });

    test('should cleanup orphaned metadata entries', () async {
      // Create a test image and save it
      final File testImageFile = File('${testDir.path}/test_cleanup.png');
      await testImageFile.writeAsBytes(
        base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
      );

      final File savedFile = await service.saveImageAndMetadata(
        imageFile: testImageFile,
        latitude: 40.7128,
        longitude: -74.0060,
        date: DateTime.now(),
      );

      final String imageHash = savedFile.path.split('/').last.split('.').first;

      // Delete the image file (leaving an orphaned Hive entry)
      await savedFile.delete();

      final int cleanedCount = await service.cleanupOrphanedMetadata();
      expect(cleanedCount, equals(1));

      // Verify entry is gone from Hive results
      final List<Map<String, dynamic>> allMetadata = await service.getAllImageMetadata();
      expect(allMetadata.any((Map<String, dynamic> m) => m['imageHash'] == imageHash), isFalse);
    });
  });
}
