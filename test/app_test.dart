import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:mural/main.dart';

class MockTileProvider extends TileProvider {
  @override
  ImageProvider getImage(TileCoordinates coordinates, TileLayer options) {
    return const AssetImage('assets/app.png');
  }
}

class FakeFirebasePlatform extends FirebasePlatform {
  @override
  FirebaseAppPlatform app([String name = defaultFirebaseAppName]) {
    return FakeFirebaseApp();
  }

  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    return FakeFirebaseApp();
  }

  @override
  List<FirebaseAppPlatform> get apps => <FirebaseAppPlatform>[FakeFirebaseApp()];
}

class FakeFirebaseApp extends FirebaseAppPlatform {
  FakeFirebaseApp()
    : super(
        'test',
        const FirebaseOptions(
          apiKey: 'test',
          appId: 'test',
          messagingSenderId: 'test',
          projectId: 'test',
        ),
      );
}

void main() {
  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    FirebasePlatform.instance = FakeFirebasePlatform();
    await Firebase.initializeApp();
    // Init Hive for tests (box used by services during app startup cleanup)
    final Directory hiveDir = await Directory.systemTemp.createTemp('mural_hive_test');
    Hive.init(hiveDir.path);
    if (!Hive.isBoxOpen('photo_metadata')) {
      await Hive.openBox('photo_metadata');
    }
  });

  tearDownAll(() async {
    if (Hive.isBoxOpen('photo_metadata')) {
      await Hive.box('photo_metadata').close();
    }
  });

  group('MyApp Widget Tests', () {
    testWidgets('creates app with title', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());

      final MaterialApp materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.title, equals('Mural'));
    });

    testWidgets('accepts custom tile provider', (WidgetTester tester) async {
      final MockTileProvider mockProvider = MockTileProvider();

      const MyApp appWithNull = MyApp(tileProvider: null);
      expect(appWithNull.tileProvider, isNull);

      final MyApp appWithProvider = MyApp(tileProvider: mockProvider);
      expect(appWithProvider.tileProvider, equals(mockProvider));
    });
  });

  group('HomePage Unit Tests', () {
    test('HomePage constructor accepts tile provider', () {
      const HomePage homePage = HomePage();
      expect(homePage.tileProvider, isNull);

      final MockTileProvider mockProvider = MockTileProvider();
      final HomePage homePageWithProvider = HomePage(tileProvider: mockProvider);
      expect(homePageWithProvider.tileProvider, equals(mockProvider));
    });
  });

  group('Widget Structure Tests', () {
    testWidgets('MyApp contains HomePage', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: Scaffold(body: Text('Test'))));
      expect(find.text('Test'), findsOneWidget);
    });
  });
}
