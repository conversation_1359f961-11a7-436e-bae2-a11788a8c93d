import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image/image.dart' as img;
import 'package:mural/services/photo_screen_service.dart';

void main() {
  group('Real Image Perspective Correction Tests', () {
    late PhotoScreenService photoScreenService;
    late File testImageFile;
    late img.Image testImage;

    setUpAll(() async {
      photoScreenService = PhotoScreenService();

      // Load the actual test image
      testImageFile = File('assets/test_image_to_crop.png');
      expect(await testImageFile.exists(), isTrue, reason: 'Test image must exist');

      final Uint8List imageBytes = await testImageFile.readAsBytes();
      testImage = img.decodePng(imageBytes)!;
      expect(testImage, isNotNull, reason: 'Test image must be decodable');
    });

    test('Real image corner detection works correctly', () async {
      final List<Offset> corners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        800, // container width
        600, // container height
      );

      expect(corners.length, equals(4));
      expect(testImage.width, equals(1090));
      expect(testImage.height, equals(1544));

      // With auto-detection enabled, corners should be detected automatically
      // Verify corners are within display bounds
      for (final Offset corner in corners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThanOrEqualTo(800));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThanOrEqualTo(600));
      }

      debugPrint('Image dimensions: ${testImage.width}x${testImage.height}');
      debugPrint('Auto-detected display corners: $corners');
    });

    test('Perspective correction with realistic corner adjustment', () async {
      // Get the display corners for the real image
      final List<Offset> displayCorners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        800, // screen width - 40 padding
        600, // screen height - 40 padding
      );

      // Simulate user adjusting corners to crop the image (inset by 10% on each side)
      final double insetX = (displayCorners[1].dx - displayCorners[0].dx) * 0.1;
      final double insetY = (displayCorners[2].dy - displayCorners[0].dy) * 0.1;

      final List<Offset> adjustedCorners = <Offset>[
        Offset(displayCorners[0].dx + insetX, displayCorners[0].dy + insetY), // Top-left inset
        Offset(displayCorners[1].dx - insetX, displayCorners[1].dy + insetY), // Top-right inset
        Offset(displayCorners[2].dx - insetX, displayCorners[2].dy - insetY), // Bottom-right inset
        Offset(displayCorners[3].dx + insetX, displayCorners[3].dy - insetY), // Bottom-left inset
      ];

      // Apply coordinate transformation (same logic as PhotoScreen)
      final double scaleX = testImage.width / (displayCorners[1].dx - displayCorners[0].dx);
      final double scaleY = testImage.height / (displayCorners[2].dy - displayCorners[0].dy);

      final List<Offset> imageCorners = adjustedCorners.map((Offset corner) {
        return Offset(
          (corner.dx - (displayCorners[0].dx + 20)) * scaleX,
          (corner.dy - (displayCorners[0].dy + 20)) * scaleY,
        );
      }).toList();

      // Verify all coordinates are within image bounds (allow some tolerance for auto-detection)
      for (final Offset corner in imageCorners) {
        expect(corner.dx, greaterThanOrEqualTo(-50)); // Allow some tolerance
        expect(corner.dx, lessThan(testImage.width + 50));
        expect(corner.dy, greaterThanOrEqualTo(-50));
        expect(corner.dy, lessThan(testImage.height + 50));
      }

      debugPrint('Adjusted corners in image coordinates: $imageCorners');

      // Calculate output dimensions
      final double minX = imageCorners.map((Offset p) => p.dx).reduce((double a, double b) => a < b ? a : b);
      final double maxX = imageCorners.map((Offset p) => p.dx).reduce((double a, double b) => a > b ? a : b);
      final double minY = imageCorners.map((Offset p) => p.dy).reduce((double a, double b) => a < b ? a : b);
      final double maxY = imageCorners.map((Offset p) => p.dy).reduce((double a, double b) => a > b ? a : b);

      final int outputWidth = (maxX - minX).round();
      final int outputHeight = (maxY - minY).round();

      // Output dimensions will vary based on auto-detected corners
      expect(outputWidth, greaterThan(800)); // Should be reasonable size
      expect(outputHeight, greaterThan(1200));
      expect(outputWidth, lessThan(testImage.width + 100)); // Allow some tolerance
      expect(outputHeight, lessThan(testImage.height + 100));

      debugPrint('Output dimensions: ${outputWidth}x$outputHeight');
    });

    test('Perspective correction produces valid cropped image', () async {
      // Simulate a perspective correction with skewed corners (like a document photo)
      final List<Offset> skewedCorners = <Offset>[
        const Offset(185, 187), // Top-left
        const Offset(939, 70), // Top-right (slightly lower)
        const Offset(929, 1500), // Bottom-right
        const Offset(164, 1437), // Bottom-left (slightly lower)
      ];

      debugPrint('Skewed corners: $skewedCorners');

      // Calculate bounding box
      final double minX = skewedCorners.map((Offset p) => p.dx).reduce((double a, double b) => a < b ? a : b);
      final double maxX = skewedCorners.map((Offset p) => p.dx).reduce((double a, double b) => a > b ? a : b);
      final double minY = skewedCorners.map((Offset p) => p.dy).reduce((double a, double b) => a < b ? a : b);
      final double maxY = skewedCorners.map((Offset p) => p.dy).reduce((double a, double b) => a > b ? a : b);

      final int outputWidth = (maxX - minX).round();
      final int outputHeight = (maxY - minY).round();

      // Create debug image showing crop zone with red lines
      final img.Image debugImage = img.Image.from(testImage);

      // Draw red lines connecting the skewed corners
      for (int i = 0; i < 4; i++) {
        final Offset start = skewedCorners[i];
        final Offset end = skewedCorners[(i + 1) % 4];
        img.drawLine(
          debugImage,
          x1: start.dx.round(),
          y1: start.dy.round(),
          x2: end.dx.round(),
          y2: end.dy.round(),
          color: img.ColorRgb8(255, 0, 0), // Red
          thickness: 3,
        );
      }

      // Save debug image
      final File debugFile = File('/Users/<USER>/src/github/mural/assets/test_image_crop_zone.jpg');
      await debugFile.writeAsBytes(img.encodeJpg(debugImage, quality: 90));
      debugPrint('Debug image with crop zone saved to: ${debugFile.path}');

      // Create output image
      final img.Image outputImage = img.Image(width: outputWidth, height: outputHeight);

      // Apply perspective correction using bilinear interpolation
      int validPixels = 0;
      for (int y = 0; y < outputHeight; y++) {
        for (int x = 0; x < outputWidth; x++) {
          final double u = x / outputWidth;
          final double v = y / outputHeight;

          // Bilinear interpolation to find source pixel
          final double sourceX =
              (skewedCorners[0].dx * (1 - u) * (1 - v)) +
              (skewedCorners[1].dx * u * (1 - v)) +
              (skewedCorners[2].dx * u * v) +
              (skewedCorners[3].dx * (1 - u) * v);

          final double sourceY =
              (skewedCorners[0].dy * (1 - u) * (1 - v)) +
              (skewedCorners[1].dy * u * (1 - v)) +
              (skewedCorners[2].dy * u * v) +
              (skewedCorners[3].dy * (1 - u) * v);

          // Verify source coordinates are valid
          if (sourceX >= 0 && sourceX < testImage.width && sourceY >= 0 && sourceY < testImage.height) {
            final img.Pixel sourcePixel = testImage.getPixel(sourceX.round(), sourceY.round());
            outputImage.setPixel(x, y, sourcePixel);
            validPixels++;
          }
        }
      }

      // Verify we processed a reasonable number of pixels
      final double validPixelRatio = validPixels / (outputWidth * outputHeight);
      expect(validPixelRatio, equals(1.0), reason: 'All pixels should be valid');
      expect(validPixels, equals(outputWidth * outputHeight));

      debugPrint(
        'Processed $validPixels valid pixels out of ${outputWidth * outputHeight} (${(validPixelRatio * 100).toStringAsFixed(1)}%)',
      );

      // Verify output image properties
      expect(outputImage.width, equals(outputWidth));
      expect(outputImage.height, equals(outputHeight));

      // Save the cropped image for visual confirmation
      final File outputFile = File('/Users/<USER>/src/github/mural/assets/test_image_cropped.jpg');
      await outputFile.writeAsBytes(img.encodeJpg(outputImage, quality: 90));
      debugPrint('Cropped image saved to: ${outputFile.path}');
    });

    test('Web scaling preserves image quality for real image', () {
      // Test web scaling logic with the actual image dimensions
      const int maxDimension = 1024;
      final int originalWidth = testImage.width;
      final int originalHeight = testImage.height;

      final double scale = maxDimension / (originalWidth > originalHeight ? originalWidth : originalHeight);
      final int scaledWidth = (originalWidth * scale).round();
      final int scaledHeight = (originalHeight * scale).round();

      // Verify scaling maintains aspect ratio
      final double originalAspect = originalWidth / originalHeight;
      final double scaledAspect = scaledWidth / scaledHeight;
      expect(scaledAspect, closeTo(originalAspect, 0.01));

      // Expected scaling: 1090x1544 -> 723x1024 (scale: 0.663)
      expect(originalWidth, equals(1090));
      expect(originalHeight, equals(1544));
      expect(scaledWidth, equals(723));
      expect(scaledHeight, equals(1024));
      expect(scale, closeTo(0.663, 0.001));
      expect(scaledHeight, equals(maxDimension));

      debugPrint('Original: ${originalWidth}x$originalHeight');
      debugPrint('Scaled: ${scaledWidth}x$scaledHeight (scale: ${scale.toStringAsFixed(3)})');
    });

    test('Corner detection handles edge cases', () async {
      // Test with extreme container dimensions
      final List<Offset> wideCorners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        1600, // Very wide container
        400, // Short container
      );

      final List<Offset> tallCorners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        400, // Narrow container
        1200, // Very tall container
      );

      // Verify both produce valid corners
      expect(wideCorners.length, equals(4));
      expect(tallCorners.length, equals(4));

      // Verify corners maintain proper relationships
      for (final List<Offset> corners in <List<Offset>>[wideCorners, tallCorners]) {
        expect(corners[0].dx, lessThan(corners[1].dx)); // Left < Right
        expect(corners[0].dy, lessThan(corners[2].dy)); // Top < Bottom
      }

      debugPrint('Wide container corners: $wideCorners');
      debugPrint('Tall container corners: $tallCorners');
    });
  });
}
