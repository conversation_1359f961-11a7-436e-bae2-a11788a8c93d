import 'dart:convert';
import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/services/mural_metadata_service.dart';
import 'package:mural/services/photo_screen_service.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockPathProviderPlatform extends Fake with MockPlatformInterfaceMixin implements PathProviderPlatform {
  @override
  Future<String?> getApplicationDocumentsPath() async {
    return '/tmp/test_documents';
  }
}

void main() {
  group('BackendSyncService File Preparation', () {
    late MuralMetadataService muralMetadataService;
    late Directory testDir;

    setUpAll(() async {
      PathProviderPlatform.instance = MockPathProviderPlatform();
      muralMetadataService = MuralMetadataService();
      testDir = Directory('/tmp/test_documents/image_cache');
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
      await testDir.create(recursive: true);

      // Init Hive for tests
      final Directory hiveDir = await Directory.systemTemp.createTemp('mural_hive_test');
      Hive.init(hiveDir.path);
      if (!Hive.isBoxOpen('photo_metadata')) {
        await Hive.openBox('photo_metadata');
      }
    });

    tearDownAll(() async {
      if (Hive.isBoxOpen('photo_metadata')) {
        await Hive.box('photo_metadata').close();
      }
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
    });

    group('Photo Upload + Metadata', () {
      test('should prepare JPG and metadata for upload', () async {
        // Create a test image file
        final File testImageFile = File('${testDir.path}/test_image.png');
        await testImageFile.writeAsBytes(
          base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
        );

        // Save image and metadata using MuralMetadataService
        final MuralMetadata metadata = MuralMetadata(
          latitude: 37.7749,
          longitude: -122.4194,
          address: 'San Francisco, CA',
          dateTaken: DateTime.now(),
          approvalStatus: 'local',
        );

        await muralMetadataService.saveImageAndMetadata(
          imageFile: testImageFile,
          latitude: metadata.latitude!,
          longitude: metadata.longitude!,
          date: metadata.dateTaken!,
          address: metadata.address!,
          approvalStatus: metadata.approvalStatus!,
        );

        // Get the saved files
        final List<Map<String, dynamic>> savedFiles = await muralMetadataService.getAllImageMetadata();
        expect(savedFiles, isNotEmpty);

        final String imageHash = savedFiles.first['imageHash'] as String;
        final String imageCacheDirPath = await muralMetadataService.getImageCacheDirPath();
        final String imagePath = '$imageCacheDirPath/$imageHash.jpg';

        // Verify JPG exists and metadata can produce serialized content
        final File savedImageFile = File(imagePath);
        expect(await savedImageFile.exists(), isTrue, reason: 'JPG file should exist');

        final MuralMetadata? meta = await muralMetadataService.getPhotoMetadata(imageHash);
        expect(meta, isNotNull);

        // Build metadata payload using PhotoScreenService like production code
        final PhotoScreenService pss = PhotoScreenService();
        final String metadataContent = await pss.createMuralMetadataContent(meta!);
        expect(metadataContent, contains('37.7749'), reason: 'Metadata should contain latitude');
        expect(metadataContent, contains('-122.4194'), reason: 'Metadata should contain longitude');
        expect(metadataContent, contains('San Francisco'), reason: 'Metadata should contain address');
      });

      test('should handle missing metadata gracefully', () async {
        // Create a test image file without serialized metadata
        final File testImageFile = File('${testDir.path}/orphan_image.png');
        await testImageFile.writeAsBytes(
          base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='),
        );

        // In a real implementation, the upload method should handle missing metadata gracefully
        // and log a warning
        expect(await testImageFile.exists(), isTrue);

        // This test verifies the system can handle orphaned images
        // In practice, all images should have valid metadata entries, but the system should be robust
      });

      test('should validate image files before upload', () async {
        // Create an invalid image file
        final File invalidImageFile = File('${testDir.path}/invalid_image.jpg');
        await invalidImageFile.writeAsBytes(<int>[0x00, 0x00, 0x00, 0x00]); // Invalid JPEG

        // The upload process should validate images and reject corrupted ones
        expect(await invalidImageFile.exists(), isTrue);

        // In a real test, we would verify that the upload method throws an exception
        // for corrupted images
      });
    });

    group('Firestore Schema', () {
      test('should include imageUrl and metadata fields', () {
        // Test data structure for Firestore
        final Map<String, dynamic> expectedFirestoreData = <String, dynamic>{
          'userId': 'test_user',
          'imageUrl': 'https://storage.googleapis.com/test/image.jpg',
          'imageHash': 'abc123',
          'latitude': 37.7749,
          'longitude': -122.4194,
          'address': 'San Francisco, CA',
          'approvalStatus': 'local',
        };

        // Verify the structure includes both URLs
        expect(expectedFirestoreData['imageUrl'], isNotNull);

        expect(expectedFirestoreData['imageHash'], isNotNull);

        // Verify URLs point to different file types
        expect(expectedFirestoreData['imageUrl'], endsWith('.jpg'));
      });
    });
  });
}
