import 'package:flutter_test/flutter_test.dart';
import 'package:mural/models/mural.dart';
import 'package:mural/models/photo.dart';
import 'package:mural/screens/photo_screen/photo_screen.dart';
import 'package:mural/widgets/photo_widget.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('PhotoScreen Navigation Fix', () {
    testWidgets('PhotoScreen should handle imageHash correctly for local photos', (WidgetTester tester) async {
      // Test that PhotoScreen can handle both full paths and hashes
      const String testImageHash = 'test_hash_123';
      const String testImagePath = '/path/to/test_hash_123.jpg';

      // Test with hash only (simulating history screen navigation)
      const PhotoScreen photoScreenWithHash = PhotoScreen(
        imageUrl: testImageHash,
        isLocal: true,
        docId: testImageHash,
      );

      // Test with full path (simulating camera screen navigation)
      const PhotoScreen photoScreenWithPath = PhotoScreen(
        imageUrl: testImagePath,
        isLocal: true,
        docId: testImageHash,
      );

      // Verify that both constructors work without throwing
      expect(photoScreenWithHash.imageUrl, equals(testImageHash));
      expect(photoScreenWithHash.isLocal, isTrue);

      expect(photoScreenWithPath.imageUrl, equals(testImagePath));
      expect(photoScreenWithPath.isLocal, isTrue);
    });

    testWidgets('PhotoWidget should handle Photo objects with hash-only correctly', (WidgetTester tester) async {
      // Create a Photo object with only imageHash (no imagePath)
      final MuralMetadata metadata = MuralMetadata(
        lastModified: DateTime.now(),
      );

      final Photo photoWithHashOnly = Photo(
        imageHash: 'test_hash_123',
        imagePath: null, // This simulates the case where only hash is provided
        metadata: metadata,
        isLocal: true,
      );

      final Photo photoWithFullPath = Photo(
        imageHash: 'test_hash_123',
        imagePath: '/path/to/test_hash_123.jpg',
        metadata: metadata,
        isLocal: true,
      );

      // Verify that both Photo objects are created correctly
      expect(photoWithHashOnly.imageHash, equals('test_hash_123'));
      expect(photoWithHashOnly.imagePath, isNull);
      expect(photoWithHashOnly.isLocal, isTrue);

      expect(photoWithFullPath.imageHash, equals('test_hash_123'));
      expect(photoWithFullPath.imagePath, equals('/path/to/test_hash_123.jpg'));
      expect(photoWithFullPath.isLocal, isTrue);

      // Test that PhotoWidget can be created with both types
      final PhotoWidget widgetWithHashOnly = PhotoWidget(photo: photoWithHashOnly);
      final PhotoWidget widgetWithFullPath = PhotoWidget(photo: photoWithFullPath);

      expect(widgetWithHashOnly.photo.imageHash, equals('test_hash_123'));
      expect(widgetWithFullPath.photo.imageHash, equals('test_hash_123'));
    });
  });
}
