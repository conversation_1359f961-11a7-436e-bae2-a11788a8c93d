import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mural/widgets/rectangle_adjuster.dart';

void main() {
  group('RectangleAdjuster Tests', () {
    testWidgets('Detec button appears when onAutoDetect is provided', (WidgetTester tester) async {
      bool autoDetectCalled = false;
      final List<Offset> initialCorners = <Offset>[
        const Offset(50, 50),
        const Offset(150, 50),
        const Offset(150, 150),
        const Offset(50, 150),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 300,
              child: RectangleAdjuster(
                initialCorners: initialCorners,
                onCornersChanged: (List<Offset> corners) {},
                onAutoDetect: () {
                  autoDetectCalled = true;
                },
              ),
            ),
          ),
        ),
      );

      // Verify the Detec button is present
      expect(find.text('Detect'), findsOneWidget);
      expect(find.byIcon(Icons.auto_fix_high), findsOneWidget);

      // Tap the Detec button
      await tester.tap(find.text('Detect'));
      await tester.pump();

      // Verify the callback was called
      expect(autoDetectCalled, isTrue);
    });

    testWidgets('Detec button does not appear when onAutoDetect is null', (WidgetTester tester) async {
      final List<Offset> initialCorners = <Offset>[
        const Offset(50, 50),
        const Offset(150, 50),
        const Offset(150, 150),
        const Offset(50, 150),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 300,
              child: RectangleAdjuster(
                initialCorners: initialCorners,
                onCornersChanged: (List<Offset> corners) {},
                // onAutoDetect is null
              ),
            ),
          ),
        ),
      );

      // Verify the Detec button is not present
      expect(find.text('Detect'), findsNothing);
      expect(find.byIcon(Icons.auto_fix_high), findsNothing);
    });

    testWidgets('Corner handles are still draggable with Detec button', (WidgetTester tester) async {
      List<Offset> currentCorners = <Offset>[
        const Offset(50, 50),
        const Offset(150, 50),
        const Offset(150, 150),
        const Offset(50, 150),
      ];
      bool cornersChanged = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 300,
              child: RectangleAdjuster(
                initialCorners: currentCorners,
                onCornersChanged: (List<Offset> corners) {
                  currentCorners = corners;
                  cornersChanged = true;
                },
                onAutoDetect: () {},
              ),
            ),
          ),
        ),
      );

      // Drag from the first corner position (50, 50)
      await tester.dragFrom(const Offset(50, 50), const Offset(10, 10));
      await tester.pump();

      // Verify the corners were actually changed by the drag
      expect(cornersChanged, isTrue);
    });
  });
}
