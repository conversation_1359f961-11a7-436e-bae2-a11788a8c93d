import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image/image.dart' as img;
import 'package:mural/services/photo_screen_service.dart';

void main() {
  group('PhotoScreen Perspective Correction Tests', () {
    late PhotoScreenService photoScreenService;
    late File testImageFile;
    late img.Image testImage;

    setUpAll(() async {
      photoScreenService = PhotoScreenService();

      // Create test image
      testImage = img.Image(width: 400, height: 300);
      img.fill(testImage, color: img.ColorRgb8(255, 255, 255));

      // Add corner markers
      img.fillRect(testImage, x1: 0, y1: 0, x2: 50, y2: 50, color: img.ColorRgb8(255, 0, 0));
      img.fillRect(testImage, x1: 350, y1: 0, x2: 400, y2: 50, color: img.ColorRgb8(0, 255, 0));
      img.fillRect(testImage, x1: 350, y1: 250, x2: 400, y2: 300, color: img.ColorRgb8(0, 0, 255));
      img.fillRect(testImage, x1: 0, y1: 250, x2: 50, y2: 300, color: img.ColorRgb8(255, 255, 0));

      // Create temporary test file
      final Directory tempDir = Directory.systemTemp;
      testImageFile = File('${tempDir.path}/test_image.jpg');
      await testImageFile.writeAsBytes(img.encodeJpg(testImage));
    });

    tearDownAll(() async {
      if (await testImageFile.exists()) {
        await testImageFile.delete();
      }
    });

    test('PhotoScreenService.calculateImageCorners returns correct corners', () async {
      final List<Offset> corners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        800, // container width
        600, // container height
      );

      expect(corners.length, equals(4));

      // Verify corners are properly ordered
      expect(corners[0].dx, lessThan(corners[1].dx)); // Top-left X < Top-right X
      expect(corners[0].dy, equals(corners[1].dy)); // Top-left Y = Top-right Y
      expect(corners[1].dx, equals(corners[2].dx)); // Top-right X = Bottom-right X
      expect(corners[1].dy, lessThan(corners[2].dy)); // Top-right Y < Bottom-right Y

      // Verify corners are within container bounds
      for (final Offset corner in corners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThanOrEqualTo(800));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThanOrEqualTo(600));
      }
    });

    test('PhotoScreenService.getImageBytes works for file paths', () async {
      final Uint8List bytes = await photoScreenService.getImageBytes(testImageFile.path);

      expect(bytes.length, greaterThan(0));

      // Verify we can decode the bytes back to an image
      final img.Image? decodedImage = img.decodeImage(bytes);
      expect(decodedImage, isNotNull);
      expect(decodedImage!.width, equals(testImage.width));
      expect(decodedImage.height, equals(testImage.height));
    });

    test('Base64 data URL parsing works correctly', () {
      // Test the base64 parsing logic that would be used on web
      final Uint8List imageBytes = img.encodeJpg(testImage);
      final String base64Image = base64Encode(imageBytes);
      final String dataUrl = 'data:image/jpeg;base64,$base64Image';

      // Extract base64 data like the web implementation does
      final String base64Data = dataUrl.split(',')[1];
      final Uint8List decodedBytes = base64Decode(base64Data);

      expect(decodedBytes.length, equals(imageBytes.length));

      // Verify decoded image matches original
      final img.Image? decodedImage = img.decodeImage(decodedBytes);
      expect(decodedImage, isNotNull);
      expect(decodedImage!.width, equals(testImage.width));
      expect(decodedImage.height, equals(testImage.height));
    });

    test('Perspective correction coordinate transformation is accurate', () async {
      // Test the actual coordinate transformation logic from PhotoScreen
      final List<Offset> displayCorners = await photoScreenService.calculateImageCorners(
        testImageFile.path,
        800, // screen width - 40 padding
        600, // screen height - 40 padding
      );

      // Simulate adjusted corners (user moved the crop handles)
      final List<Offset> adjustedCorners = <Offset>[
        Offset(displayCorners[0].dx + 20, displayCorners[0].dy + 20), // Inset from corners
        Offset(displayCorners[1].dx - 20, displayCorners[1].dy + 20),
        Offset(displayCorners[2].dx - 20, displayCorners[2].dy - 20),
        Offset(displayCorners[3].dx + 20, displayCorners[3].dy - 20),
      ];

      // Apply the same transformation logic as PhotoScreen._applyRectangleAdjustment
      final double scaleX = testImage.width / (displayCorners[1].dx - displayCorners[0].dx);
      final double scaleY = testImage.height / (displayCorners[2].dy - displayCorners[0].dy);

      final List<Offset> imageCorners = adjustedCorners.map((Offset corner) {
        return Offset(
          (corner.dx - (displayCorners[0].dx + 20)) * scaleX,
          (corner.dy - (displayCorners[0].dy + 20)) * scaleY,
        );
      }).toList();

      // Verify all image coordinates are within bounds
      for (final Offset corner in imageCorners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThan(testImage.width));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThan(testImage.height));
      }

      // Verify the transformation maintains relative positioning
      expect(imageCorners[0].dx, lessThan(imageCorners[1].dx)); // Left < Right
      expect(imageCorners[0].dy, lessThan(imageCorners[2].dy)); // Top < Bottom
    });

    test('Bilinear interpolation produces valid pixel coordinates', () {
      // Test the bilinear interpolation used in _processImageOnWeb and _processImageOnMobile
      final List<Offset> imageCorners = <Offset>[
        const Offset(50, 50), // Top-left (cropped)
        const Offset(350, 60), // Top-right (slightly skewed)
        const Offset(340, 250), // Bottom-right
        const Offset(60, 240), // Bottom-left
      ];

      const int outputWidth = 200;
      const int outputHeight = 150;

      // Test several points across the output image
      for (int y = 0; y < outputHeight; y += 25) {
        for (int x = 0; x < outputWidth; x += 25) {
          final double u = x / outputWidth;
          final double v = y / outputHeight;

          // Apply the same bilinear interpolation as the actual code
          final double originalX =
              (imageCorners[0].dx * (1 - u) * (1 - v)) +
              (imageCorners[1].dx * u * (1 - v)) +
              (imageCorners[2].dx * u * v) +
              (imageCorners[3].dx * (1 - u) * v);

          final double originalY =
              (imageCorners[0].dy * (1 - u) * (1 - v)) +
              (imageCorners[1].dy * u * (1 - v)) +
              (imageCorners[2].dy * u * v) +
              (imageCorners[3].dy * (1 - u) * v);

          // Verify coordinates are within the original image bounds
          expect(originalX, greaterThanOrEqualTo(0));
          expect(originalX, lessThan(testImage.width));
          expect(originalY, greaterThanOrEqualTo(0));
          expect(originalY, lessThan(testImage.height));

          // Verify we can safely get a pixel at these coordinates
          final img.Pixel pixel = testImage.getPixel(originalX.round(), originalY.round());
          expect(pixel, isNotNull);
        }
      }
    });

    test('Web scaling logic matches implementation', () {
      // Test the web scaling logic from _processImageOnWeb
      const int maxDimension = 1024;

      // Test with large image
      const int largeWidth = 2000;
      const int largeHeight = 1500;

      final double scale = maxDimension / (largeWidth > largeHeight ? largeWidth : largeHeight);
      final int scaledWidth = (largeWidth * scale).round();
      final int scaledHeight = (largeHeight * scale).round();

      expect(scaledWidth, equals(1024));
      expect(scaledHeight, equals(768));

      // Test with small image (should not be scaled up)
      const int smallWidth = 500;
      const int smallHeight = 400;

      final double smallScale = maxDimension / (smallWidth > smallHeight ? smallWidth : smallHeight);
      final int smallScaledWidth = (smallWidth * smallScale).round();
      final int smallScaledHeight = (smallHeight * smallScale).round();

      // Small images should be scaled up to max dimension
      expect(smallScaledWidth, equals(1024));
      expect(smallScaledHeight, equals(819)); // Maintains aspect ratio
    });

    test('Chunk processing boundaries are correct', () {
      // Test the chunk processing logic from _processImageOnWeb
      const int imageHeight = 1000;
      const int chunkSize = 100;

      final List<List<int>> chunks = <List<int>>[];

      for (int startY = 0; startY < imageHeight; startY += chunkSize) {
        final int endY = (startY + chunkSize < imageHeight) ? startY + chunkSize : imageHeight;
        chunks.add(<int>[startY, endY]);
      }

      expect(chunks.length, equals(10)); // Should have 10 chunks
      expect(chunks.first[0], equals(0)); // First chunk starts at 0
      expect(chunks.last[1], equals(1000)); // Last chunk ends at image height

      // Verify no gaps between chunks
      for (int i = 1; i < chunks.length; i++) {
        expect(chunks[i][0], equals(chunks[i - 1][1])); // Current start = previous end
      }

      // Verify all chunks are within bounds
      for (final List<int> chunk in chunks) {
        expect(chunk[0], greaterThanOrEqualTo(0));
        expect(chunk[1], lessThanOrEqualTo(imageHeight));
        expect(chunk[1], greaterThan(chunk[0])); // End > Start
      }
    });
  });
}
