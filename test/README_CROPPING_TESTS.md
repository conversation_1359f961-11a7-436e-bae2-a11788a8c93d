# Photo Screen Test Suite

## Overview

This test suite validates the photo screen functionality, including manual corner adjustment, coordinate transformations, and image processing for the Mural app.

⚠️ **Missing Feature**: Automatic edge detection is not yet implemented. The app currently requires manual corner adjustment.

## Active Test Files

- `photo_screen_perspective_test.dart` - Tests PhotoScreenService integration
- `real_image_perspective_test.dart` - Tests with real image and verified coordinates
- `edge_detection_test.dart` - Tests for automatic edge detection (⚠️ **NOT YET IMPLEMENTED**)

## Test Coverage

### PhotoScreen Service Tests (`photo_screen_perspective_test.dart`)

1. **PhotoScreenService.calculateImageCorners**
   - Tests corner calculation with actual PhotoScreenService implementation
   - Verifies corners are properly ordered and within container bounds
   - Uses synthetic test image (400x300) with colored corner markers

2. **PhotoScreenService.getImageBytes**
   - Tests image byte extraction from file paths
   - Verifies decoded images match original dimensions
   - Ensures proper file handling

3. **Base64 Data URL Parsing**
   - Tests web-specific base64 parsing logic
   - Verifies round-trip encoding/decoding
   - Ensures image dimensions are preserved

4. **Coordinate Transformation Accuracy**
   - Tests the actual transformation logic from PhotoScreen
   - Verifies scale factors and padding offsets
   - Ensures all coordinates remain within image bounds

5. **Bilinear Interpolation**
   - Tests interpolation with realistic corner coordinates
   - Verifies pixel coordinate validity
   - Ensures mathematical accuracy

6. **Web Scaling Logic**
   - Tests scaling for both large and small images
   - Verifies 1024px maximum dimension constraint
   - Ensures aspect ratio preservation

7. **Chunk Processing Boundaries**
   - Tests web chunk processing logic (100-row chunks)
   - Verifies no gaps between chunks
   - Ensures proper boundary handling

### Real Image Tests (`real_image_perspective_test.dart`)

1. **Real Image Corner Detection**
   - Tests with actual test_image_to_crop.png (1090x1544)
   - Verifies expected corner coordinates: (188.2,0), (611.8,0), (611.8,600), (188.2,600)
   - Validates display calculations for 800x600 container

2. **Realistic Corner Adjustment**
   - Simulates 10% inset cropping
   - Tests coordinate transformation with real image dimensions
   - Verifies output dimensions: 872x1235

3. **Perspective Correction with Manually Verified Corners**
   - Uses approved skewed corners: (185,187), (939,70), (929,1500), (164,1437)
   - Produces verified output dimensions: 775x1430
   - Tests bilinear interpolation with real image data

4. **Regression Test for Verified Corners**
   - Prevents regression of manually approved corner coordinates
   - Validates exact bounding box calculations
   - Ensures output dimensions remain 775x1430

5. **Bilinear Interpolation Accuracy**
   - Tests interpolation math with verified corners
   - Validates corner point mapping
   - Ensures center point calculations are within bounds

### Edge Detection Tests (`edge_detection_test.dart`) - ⚠️ **NOT YET IMPLEMENTED**

1. **Automatic Edge Detection**
   - Should detect rectangular shapes in images
   - Should return valid corner coordinates
   - Should handle invalid image data gracefully

2. **Document Boundary Detection**
   - Should detect document-like rectangular shapes
   - Should work with white documents on colored backgrounds
   - Should handle various document orientations

3. **Skewed Document Detection**
   - Should detect perspective-distorted rectangles
   - Should return corners for skewed documents
   - Should work with camera-captured documents

4. **Noise Filtering**
   - Should detect clear shapes despite image noise
   - Should filter out irrelevant edges
   - Should prioritize large rectangular contours

5. **Performance Testing**
   - Should complete edge detection within reasonable time
   - Should handle large images efficiently
   - Should not block the UI thread

## Implementation Status

### ✅ Implemented Features

- **Manual Corner Adjustment**: Users can manually drag corners to crop images
- **Coordinate Transformations**: Screen-to-image coordinate mapping works correctly
- **Bilinear Interpolation**: Perspective correction math is accurate
- **Service Integration**: PhotoScreenService methods work with real files
- **Regression Prevention**: Manually verified coordinates are preserved

### ⚠️ Missing Features

- **Automatic Edge Detection**: No computer vision algorithms implemented
- **Document Boundary Detection**: Cannot automatically find rectangular shapes
- **Corner Detection**: No Harris corners or FAST corner detection
- **Image Preprocessing**: No grayscale conversion, blur, or threshold operations
- **Canny Edge Detection**: Core edge detection algorithm not implemented
- **Contour Detection**: Cannot find and analyze shape contours

### 🔧 Required Implementation

To complete the automatic edge detection feature, implement:

1. **EdgeDetectionService.detectEdges()** - Main entry point for automatic detection
2. **Image preprocessing** - Grayscale, blur, threshold operations
3. **Canny edge detection** - Industry-standard edge detection algorithm
4. **Contour detection** - Find closed shapes in edge-detected images
5. **Rectangle detection** - Identify 4-sided polygons from contours
6. **Corner refinement** - Improve corner accuracy using sub-pixel methods

## Running the Tests

```bash
# Run all photo screen tests
flutter test test/photo_screen_perspective_test.dart test/real_image_perspective_test.dart

# Run edge detection tests (will show unimplemented errors)
flutter test test/edge_detection_test.dart

# Run individual test suites
flutter test test/photo_screen_perspective_test.dart
flutter test test/real_image_perspective_test.dart
```

## Test Results

### ✅ Passing Tests (Manual Corner Adjustment)

**PhotoScreen Service Tests**: All pass
- Service Integration: PhotoScreenService methods work with real files
- Coordinate Accuracy: Transformations produce expected results
- Web Compatibility: Base64 parsing and chunk processing work correctly

**Real Image Tests**: All pass
- Image Dimensions: 1090x1544 pixels (test_image_to_crop.png)
- Display Corners: (188.2,0), (611.8,0), (611.8,600), (188.2,600) for 800x600 container
- 10% Inset Cropping: Produces 872x1235 output
- Verified Perspective Correction: Corners (185,187), (939,70), (929,1500), (164,1437) → 775x1430 output
- Regression Protection: Manually approved coordinates are preserved

### ⚠️ Failing Tests (Automatic Edge Detection)

**Edge Detection Tests**: All throw `UnimplementedError`
- Automatic edge detection not implemented
- Document boundary detection not implemented
- Corner detection algorithms not implemented
- Image preprocessing not implemented

**Next Steps**: Implement the EdgeDetectionService methods to make these tests pass.

### Current Limitations

- **Manual Process**: Users must manually adjust all four corners
- **No Initial Suggestion**: App doesn't provide starting corner positions
- **No Computer Vision**: Missing industry-standard edge detection algorithms
- **Poor UX**: Users have to guess where document edges are located
