{"flutter": {"platforms": {"dart": {"lib/firebase_options.dart": {"projectId": "vteam-mural", "configurations": {"android": "1:79912496559:android:15ad8469e1850ed8ca935a", "ios": "1:79912496559:ios:eefe79997df543ecca935a", "macos": "1:79912496559:ios:eefe79997df543ecca935a", "web": "1:79912496559:web:d262fcd8c49aa925ca935a"}}}, "android": {"default": {"projectId": "vteam-mural", "appId": "1:79912496559:android:15ad8469e1850ed8ca935a", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "vteam-mural", "appId": "1:79912496559:ios:eefe79997df543ecca935a", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "vteam-mural", "appId": "1:79912496559:ios:eefe79997df543ecca935a", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "web": {"default": {"projectId": "vteam-mural", "appId": "1:79912496559:web:d262fcd8c49aa925ca935a"}}}}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}}