# Mural

## Overview

Mural is a mobile application for discovering and collecting city murals and graffiti. It allows users to:

* Find murals and graffiti in their city.
* Add murals and graffiti to a personal collection.
* Share discoveries with friends.

## Technologies Used

* Flutter
* Firebase

## Getting Started

### Prerequisites

* Flutter SDK
* Firebase project

### Installation

1. Clone the repository:

    ```sh
    git clone [repository URL]
    ```

2. Navigate to the project directory:

    ```sh
    cd mural
    ```

3. Install dependencies:

    ```sh
    flutter pub get
    ```

4. Configure Firebase:

    * Create a Firebase project in the [Firebase Console](https://console.firebase.google.com/).
    * Enable the necessary Firebase services (e.g., Authentication, Firestore).
    * Download the `google-services.json` (for Android) and `GoogleService-Info.plist` (for iOS) configuration files.
    * Place the configuration files in the appropriate directories in the Flutter project.
5. Run the app:

    ```sh
    flutter run
    ```

## Photo and Metadata Architecture

Mural uses a local-first architecture with Hive-backed metadata storage for comprehensive photo metadata management. This provides rich metadata capabilities, offline editing, and selective cloud synchronization. A unified `PhotoService` and `Photo` data model streamline photo-related operations from caching and validation to display.

### Photo Editing & Deletion Permissions

* **Editing**: Owners can edit photos until approval; reviewers can edit anytime
* **Deletion**: Owners can delete pending/rejected photos; reviewers can delete anytime
* **Cross-Platform**: All permissions work on both mobile and web clients

### Core System: Photo + Metadata

Every photo in Mural is stored as a JPG image with metadata stored in Hive, keyed by the image hash.

1. JPG Image File: `{hash}.jpg` - The actual photo, always converted to JPG format for consistency.
2. Metadata (Hive): Stored in the local database for latitude/longitude, address, date taken, crop corners, approval status, and user info.

All items use SHA256 hash-based naming to prevent filename collisions and enable content-based deduplication.

### Local Storage Structure

```text
Documents/image_cache/
├── a1b2c3d4e5f6.jpg     # Photo file
├── a1b2c3d4e5f6.meta    # Metadata stored in Hive (logical entry)
├── f6e5d4c3b2a1.jpg     # Another photo
├── f6e5d4c3b2a1.meta    # Its metadata (Hive)
└── ...
```

### Metadata Contents

Each photo has comprehensive metadata stored in Hive:

* Location Data: GPS coordinates, geocoded address
* Temporal Data: Date/time photo was taken
* Processing Data: Crop corners, distortion correction settings
* Workflow Data: Approval status, submission state
* **Technical Data**: Original filename, processing history

### Cloud Synchronization

When photos are submitted to Firebase, the JPG and key metadata are uploaded:

* Firebase Storage: Stores the `.jpg` file.
* Firestore: Contains a document with the image URL and searchable metadata.

```javascript
// Firestore document structure
{
  userId: "user123",
  imageUrl: "https://storage.googleapis.com/.../hash.jpg",
  imageHash: "a1b2c3d4e5f6...",
  latitude: 37.7749,
  longitude: -122.4194,
  address: "San Francisco, CA",
  approvalStatus: "pending",
  timestamp: "2024-01-15T10:30:00Z"
}
```

### Key Benefits

* Rich Metadata: Hive-backed metadata supports extensive fields.
* Offline Capable: Full photo editing and metadata management without internet.
* Deduplication: Hash-based naming prevents duplicate uploads.
* Selective Sync: Users choose which photos to submit to the cloud.
* Data Portability: Export/import tools can serialize metadata when needed.
* Robust Recovery: Automatic cleanup of corrupted images and orphaned metadata. The system can also recreate missing metadata from EXIF data stored in the JPG, enhancing data integrity.

### Key Architecture Components

* **`Photo`** (`lib/models/photo.dart`): The unified data model representing a photo, whether it's stored locally or remotely. It encapsulates the image hash, paths, URLs, and its associated `PhotoMetadata`.
* **`PhotoService`** (`lib/services/photo_service.dart`): A centralized service that handles all photo-related operations, including fetching, caching, validation, and cleanup of corrupted images. It replaces the previous `ImageCacheService`.
* `MuralMetadataService` (`lib/services/mural_metadata_service.dart`): Manages metadata read/write via Hive and can recreate missing metadata from JPG EXIF data.
* `BackendSyncService` (`lib/services/backend_sync_service.dart`): Manages the upload of JPG to Firebase Storage and metadata to Firestore.
* **`PhotoWidget`** (`lib/widgets/photo_widget.dart`): The primary widget for displaying a `Photo` object. It handles loading from local or cached sources and displays error states gracefully.

## License

All rights reserved. No part of this software, including its code, design, or any associated assets, may be reproduced, distributed, or transmitted in any form or by any means, without the prior written permission of the copyright holder. For inquiries regarding licensing or usage, please contact **JP Duplessis VTeam.com** at <<EMAIL>>
