import Cocoa
import FlutterMacOS
import FirebaseCore
import GoogleSignIn

@main
class AppDelegate: FlutterAppDelegate {
  override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
    return true
  }

  override func applicationSupportsSecureRestorableState(_ app: NSApplication) -> <PERSON><PERSON> {
    return true
  }

  override func applicationDidFinishLaunching(_ notification: Notification) {
    FirebaseApp.configure()
    GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: "79912496559-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com")
    GIDSignIn.sharedInstance.restorePreviousSignIn()
  }

  override func application(_ app: NSApplication, open urls: [URL]) {
    for url in urls {
        if GIDSignIn.sharedInstance.handle(url) {
            return
        }
    }
  }
}
