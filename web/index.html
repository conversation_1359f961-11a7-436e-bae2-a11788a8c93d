<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="mural">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <meta name="google-signin-client_id" content="***********-crtrtpm7mm8pehnt4b4qi331e7aqrcga.apps.googleusercontent.com">
  <title>mural</title>
  <link rel="manifest" href="manifest.json">
  
  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>
  
  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyB6S2VXUii_bqpOlipmI1Rg5H8BUdCaDKw",
      authDomain: "vteam-mural.firebaseapp.com",
      projectId: "vteam-mural",
      storageBucket: "vteam-mural.firebasestorage.app",
      messagingSenderId: "***********",
      appId: "1:***********:web:d262fcd8c49aa925ca935a",
      measurementId: "G-T5GK30711H"
    };
    
    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>
  <script src="firebase-config.js"></script>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
