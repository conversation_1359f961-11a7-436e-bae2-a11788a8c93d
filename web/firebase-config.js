// Firebase Web Configuration
// This file ensures proper Firebase initialization for web

// Check if Firebase is already initialized
if (!firebase.apps.length) {
  const firebaseConfig = {
    apiKey: "AIzaSyB6S2VXUii_bqpOlipmI1Rg5H8BUdCaDKw",
    authDomain: "vteam-mural.firebaseapp.com",
    projectId: "vteam-mural",
    storageBucket: "vteam-mural.firebasestorage.app",
    messagingSenderId: "79912496559",
    appId: "1:79912496559:web:d262fcd8c49aa925ca935a",
    measurementId: "G-T5GK30711H"
  };
  
  firebase.initializeApp(firebaseConfig);
}

// Configure Auth settings for web
firebase.auth().useDeviceLanguage();

// Enable persistence for better user experience
firebase.auth().setPersistence(firebase.auth.Auth.Persistence.LOCAL);

// Handle auth state changes
firebase.auth().onAuthStateChanged((user) => {
  if (user) {
    console.log('User signed in:', user.email);
  } else {
    console.log('User signed out');
  }
});