digraph "" {
  graph [style=rounded fontname="Arial Black" fontsize=13 penwidth=2.6];
  node [shape=rect style="filled,rounded" fontname=Arial fontsize=15 fillcolor=Lavender penwidth=1.3];
  edge [penwidth=1.3];
  "/test/mural_metadata_service_test.dart" [label="mural_metadata_service_test"];
  "/test/history_screen_count_test.dart" [label="history_screen_count_test"];
  "/test/app_test.dart" [label="app_test"];
  "/test/real_image_perspective_test.dart" [label="real_image_perspective_test"];
  "/test/mural_metadata_recreation_test.dart" [label="mural_metadata_recreation_test"];
  "/test/photo_screen_perspective_test.dart" [label="photo_screen_perspective_test"];
  "/test/edge_detection_test.dart" [label="edge_detection_test"];
  "/test/rectangle_adjuster_test.dart" [label="rectangle_adjuster_test"];
  "/test/photo_screen_navigation_test.dart" [label="photo_screen_navigation_test"];
  "/test/backend_sync_service_test.dart" [label="backend_sync_service_test"];
  "/lib/constants/my_colors.dart" [label="my_colors"];
  "/lib/constants/status_constants.dart" [label="status_constants"];
  "/lib/firebase_options.dart" [label="firebase_options"];
  "/lib/models/photo.dart" [label="photo"];
  "/lib/models/mural.dart" [label="mural"];
  "/lib/screens/signin_screen.dart" [label="signin_screen"];
  "/lib/screens/photo_screen/photo_screen.dart" [label="photo_screen"];
  "/lib/screens/photo_screen/photo_action_panel.dart" [label="photo_action_panel"];
  "/lib/screens/photo_screen/photo_viewer.dart" [label="photo_viewer"];
  "/lib/screens/photo_screen/photo_top_controls.dart" [label="photo_top_controls"];
  "/lib/screens/photo_screen/photo_editing_view.dart" [label="photo_editing_view"];
  "/lib/screens/settings_screen.dart" [label="settings_screen"];
  "/lib/screens/map_screen.dart" [label="map_screen"];
  "/lib/screens/camera_screen.dart" [label="camera_screen"];
  "/lib/screens/review_screen.dart" [label="review_screen"];
  "/lib/screens/address_screen.dart" [label="address_screen"];
  "/lib/screens/collection_screen.dart" [label="collection_screen"];
  "/lib/main.dart" [label="main"];
  "/lib/navigator_key.dart" [label="navigator_key"];
  "/lib/services/photo_screen_service.dart" [label="photo_screen_service"];
  "/lib/services/photo_service.dart" [label="photo_service"];
  "/lib/services/backend_sync_service.dart" [label="backend_sync_service"];
  "/lib/services/auth_service.dart" [label="auth_service"];
  "/lib/services/edge_detection_service.dart" [label="edge_detection_service"];
  "/lib/services/mural_metadata_service.dart" [label="mural_metadata_service"];
  "/lib/widgets/photo_widget.dart" [label="photo_widget"];
  "/lib/widgets/working.dart" [label="working"];
  "/lib/widgets/my_message.dart" [label="my_message"];
  "/lib/widgets/my_button.dart" [label="my_button"];
  "/lib/widgets/responsive_photo_list.dart" [label="responsive_photo_list"];
  "/lib/widgets/spray.dart" [label="spray"];
  "/lib/widgets/rectangle_adjuster.dart" [label="rectangle_adjuster"];
  "/lib/widgets/shutter_button.dart" [label="shutter_button"];
  "/lib/widgets/adaptive_landsacpe.dart" [label="adaptive_landsacpe"];
  "/lib/widgets/gradient_screen.dart" [label="gradient_screen"];
  "/lib/widgets/buttons_selection.dart" [label="buttons_selection"];
  "/lib/widgets/photo_list_item.dart" [label="photo_list_item"];
  subgraph "cluster~" {
    label="mural";
    subgraph "cluster~/test" {
      label="test";
      "/test/mural_metadata_service_test.dart";
      "/test/history_screen_count_test.dart";
      "/test/app_test.dart";
      "/test/real_image_perspective_test.dart";
      "/test/mural_metadata_recreation_test.dart";
      "/test/photo_screen_perspective_test.dart";
      "/test/edge_detection_test.dart";
      "/test/rectangle_adjuster_test.dart";
      "/test/photo_screen_navigation_test.dart";
      "/test/backend_sync_service_test.dart";
    }
    subgraph "cluster~/lib" {
      label="lib";
      "/lib/firebase_options.dart";
      "/lib/main.dart";
      "/lib/navigator_key.dart";
      subgraph "cluster~/lib/constants" {
        label="constants";
        "/lib/constants/my_colors.dart";
        "/lib/constants/status_constants.dart";
      }
      subgraph "cluster~/lib/models" {
        label="models";
        "/lib/models/photo.dart";
        "/lib/models/mural.dart";
      }
      subgraph "cluster~/lib/screens" {
        label="screens";
        "/lib/screens/signin_screen.dart";
        "/lib/screens/settings_screen.dart";
        "/lib/screens/map_screen.dart";
        "/lib/screens/camera_screen.dart";
        "/lib/screens/review_screen.dart";
        "/lib/screens/address_screen.dart";
        "/lib/screens/collection_screen.dart";
        subgraph "cluster~/lib/screens/photo_screen" {
          label="photo_screen";
          "/lib/screens/photo_screen/photo_screen.dart";
          "/lib/screens/photo_screen/photo_action_panel.dart";
          "/lib/screens/photo_screen/photo_viewer.dart";
          "/lib/screens/photo_screen/photo_top_controls.dart";
          "/lib/screens/photo_screen/photo_editing_view.dart";
        }
      }
      subgraph "cluster~/lib/services" {
        label="services";
        "/lib/services/photo_screen_service.dart";
        "/lib/services/photo_service.dart";
        "/lib/services/backend_sync_service.dart";
        "/lib/services/auth_service.dart";
        "/lib/services/edge_detection_service.dart";
        "/lib/services/mural_metadata_service.dart";
      }
      subgraph "cluster~/lib/widgets" {
        label="widgets";
        "/lib/widgets/photo_widget.dart";
        "/lib/widgets/working.dart";
        "/lib/widgets/my_message.dart";
        "/lib/widgets/my_button.dart";
        "/lib/widgets/responsive_photo_list.dart";
        "/lib/widgets/spray.dart";
        "/lib/widgets/rectangle_adjuster.dart";
        "/lib/widgets/shutter_button.dart";
        "/lib/widgets/adaptive_landsacpe.dart";
        "/lib/widgets/gradient_screen.dart";
        "/lib/widgets/buttons_selection.dart";
        "/lib/widgets/photo_list_item.dart";
      }
    }
  }
  "/test/mural_metadata_service_test.dart" -> "/lib/models/mural.dart";
  "/test/mural_metadata_service_test.dart" -> "/lib/services/mural_metadata_service.dart";
  "/test/history_screen_count_test.dart" -> "/lib/constants/status_constants.dart";
  "/test/app_test.dart" -> "/lib/main.dart";
  "/test/real_image_perspective_test.dart" -> "/lib/services/photo_screen_service.dart";
  "/test/mural_metadata_recreation_test.dart" -> "/lib/models/mural.dart";
  "/test/mural_metadata_recreation_test.dart" -> "/lib/services/mural_metadata_service.dart";
  "/test/photo_screen_perspective_test.dart" -> "/lib/services/photo_screen_service.dart";
  "/test/edge_detection_test.dart" -> "/lib/services/edge_detection_service.dart";
  "/test/rectangle_adjuster_test.dart" -> "/lib/widgets/rectangle_adjuster.dart";
  "/test/photo_screen_navigation_test.dart" -> "/lib/models/mural.dart";
  "/test/photo_screen_navigation_test.dart" -> "/lib/models/photo.dart";
  "/test/photo_screen_navigation_test.dart" -> "/lib/screens/photo_screen/photo_screen.dart";
  "/test/photo_screen_navigation_test.dart" -> "/lib/widgets/photo_widget.dart";
  "/test/backend_sync_service_test.dart" -> "/lib/models/mural.dart";
  "/test/backend_sync_service_test.dart" -> "/lib/services/mural_metadata_service.dart";
  "/test/backend_sync_service_test.dart" -> "/lib/services/photo_screen_service.dart";
  "/lib/models/photo.dart" -> "/lib/models/mural.dart";
  "/lib/models/photo.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/models/photo.dart" -> "/lib/services/photo_service.dart";
  "/lib/screens/signin_screen.dart" -> "/lib/services/auth_service.dart";
  "/lib/screens/signin_screen.dart" -> "/lib/widgets/my_message.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/constants/status_constants.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/models/mural.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/models/photo.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/screens/address_screen.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/screens/photo_screen/photo_action_panel.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/screens/photo_screen/photo_editing_view.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/screens/photo_screen/photo_top_controls.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/screens/photo_screen/photo_viewer.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/services/auth_service.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/services/backend_sync_service.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/services/photo_screen_service.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/widgets/my_message.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/widgets/photo_widget.dart";
  "/lib/screens/photo_screen/photo_screen.dart" -> "/lib/widgets/working.dart";
  "/lib/screens/photo_screen/photo_action_panel.dart" -> "/lib/widgets/my_button.dart";
  "/lib/screens/photo_screen/photo_action_panel.dart" -> "/lib/widgets/working.dart";
  "/lib/screens/photo_screen/photo_viewer.dart" -> "/lib/models/mural.dart";
  "/lib/screens/photo_screen/photo_top_controls.dart" -> "/lib/widgets/my_button.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/models/mural.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/models/photo.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/services/photo_screen_service.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/widgets/adaptive_landsacpe.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/widgets/my_button.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/widgets/photo_widget.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/widgets/rectangle_adjuster.dart";
  "/lib/screens/photo_screen/photo_editing_view.dart" -> "/lib/widgets/working.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/models/mural.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/screens/signin_screen.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/services/auth_service.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/services/backend_sync_service.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/settings_screen.dart" -> "/lib/widgets/my_message.dart";
  "/lib/screens/map_screen.dart" -> "/lib/constants/status_constants.dart";
  "/lib/screens/map_screen.dart" -> "/lib/screens/photo_screen/photo_screen.dart";
  "/lib/screens/map_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/constants/my_colors.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/buttons_selection.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/my_message.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/shutter_button.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/spray.dart";
  "/lib/screens/camera_screen.dart" -> "/lib/widgets/working.dart";
  "/lib/screens/review_screen.dart" -> "/lib/models/photo.dart";
  "/lib/screens/review_screen.dart" -> "/lib/screens/photo_screen/photo_screen.dart";
  "/lib/screens/review_screen.dart" -> "/lib/screens/signin_screen.dart";
  "/lib/screens/review_screen.dart" -> "/lib/services/photo_service.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/buttons_selection.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/my_button.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/photo_list_item.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/responsive_photo_list.dart";
  "/lib/screens/review_screen.dart" -> "/lib/widgets/working.dart";
  "/lib/screens/address_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/address_screen.dart" -> "/lib/widgets/my_button.dart";
  "/lib/screens/address_screen.dart" -> "/lib/widgets/my_message.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/constants/status_constants.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/models/photo.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/screens/photo_screen/photo_screen.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/screens/signin_screen.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/services/photo_service.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/widgets/buttons_selection.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/widgets/photo_list_item.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/widgets/responsive_photo_list.dart";
  "/lib/screens/collection_screen.dart" -> "/lib/widgets/working.dart";
  "/lib/main.dart" -> "/lib/constants/my_colors.dart";
  "/lib/main.dart" -> "/lib/navigator_key.dart";
  "/lib/main.dart" -> "/lib/screens/camera_screen.dart";
  "/lib/main.dart" -> "/lib/screens/collection_screen.dart";
  "/lib/main.dart" -> "/lib/screens/map_screen.dart";
  "/lib/main.dart" -> "/lib/screens/review_screen.dart";
  "/lib/main.dart" -> "/lib/screens/settings_screen.dart";
  "/lib/main.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/main.dart" -> "/lib/services/photo_service.dart";
  "/lib/main.dart" -> "/lib/widgets/adaptive_landsacpe.dart";
  "/lib/main.dart" -> "/lib/widgets/buttons_selection.dart";
  "/lib/main.dart" -> "/lib/widgets/gradient_screen.dart";
  "/lib/main.dart" -> "/lib/widgets/my_message.dart";
  "/lib/main.dart" -> "/lib/firebase_options.dart";
  "/lib/services/photo_screen_service.dart" -> "/lib/models/mural.dart";
  "/lib/services/photo_screen_service.dart" -> "/lib/services/edge_detection_service.dart";
  "/lib/services/photo_screen_service.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/services/photo_screen_service.dart" -> "/lib/widgets/my_message.dart";
  "/lib/services/photo_service.dart" -> "/lib/models/mural.dart";
  "/lib/services/photo_service.dart" -> "/lib/models/photo.dart";
  "/lib/services/photo_service.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/services/backend_sync_service.dart" -> "/lib/models/mural.dart";
  "/lib/services/backend_sync_service.dart" -> "/lib/services/mural_metadata_service.dart";
  "/lib/services/backend_sync_service.dart" -> "/lib/services/photo_screen_service.dart";
  "/lib/services/mural_metadata_service.dart" -> "/lib/models/mural.dart";
  "/lib/widgets/photo_widget.dart" -> "/lib/models/mural.dart";
  "/lib/widgets/photo_widget.dart" -> "/lib/models/photo.dart";
  "/lib/widgets/photo_widget.dart" -> "/lib/widgets/my_button.dart";
  "/lib/widgets/photo_widget.dart" -> "/lib/widgets/working.dart";
  "/lib/widgets/my_message.dart" -> "/lib/navigator_key.dart";
  "/lib/widgets/my_button.dart" -> "/lib/constants/my_colors.dart";
  "/lib/widgets/gradient_screen.dart" -> "/lib/constants/my_colors.dart";
  "/lib/widgets/buttons_selection.dart" -> "/lib/constants/my_colors.dart";
  "/lib/widgets/photo_list_item.dart" -> "/lib/models/photo.dart";
  "/lib/widgets/photo_list_item.dart" -> "/lib/widgets/my_button.dart";
  "/lib/widgets/photo_list_item.dart" -> "/lib/widgets/photo_widget.dart";
}
